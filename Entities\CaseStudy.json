{"name": "CaseStudy", "type": "object", "properties": {"client_name": {"type": "string", "description": "Name of the client company"}, "client_logo_url": {"type": "string", "description": "URL to client's logo"}, "industry": {"type": "string", "description": "Client's industry"}, "challenge": {"type": "string", "description": "The problem or challenge the client faced"}, "solution": {"type": "string", "description": "Our strategic solution and approach"}, "results": {"type": "array", "items": {"type": "object", "properties": {"metric": {"type": "string"}, "value": {"type": "string"}, "improvement": {"type": "string"}}}, "description": "Measurable results achieved"}, "featured_image_url": {"type": "string", "description": "Main case study image"}, "testimonial": {"type": "string", "description": "Client testimonial quote"}, "testimonial_author": {"type": "string", "description": "Who said the testimonial"}, "services_used": {"type": "array", "items": {"type": "string"}, "description": "Services we provided"}}, "required": ["client_name", "challenge", "solution", "results"]}