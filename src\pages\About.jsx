import React from "react";
import { Link } from "react-router-dom";
import { teamMembers } from "@/data/teamMembers";
import { createPageUrl } from "@/utils";
import { ArrowUpRight, Target, Users, TrendingUp, Zap } from "lucide-react";

export default function About() {
  const stats = [
    { number: "50+", label: "BRANDS SCALED" },
    { number: "$2M+", label: "AD SPEND MANAGED" },
    { number: "500K+", label: "FOLLOWERS GROWN" },
    { number: "3.2X", label: "AVERAGE ROAS" }
  ];

  const values = [
    {
      icon: Target,
      title: "RESULTS-DRIVEN",
      description: "We don't care about vanity metrics. Every strategy is designed to generate real revenue."
    },
    {
      icon: Users,
      title: "CLIENT-FOCUSED",
      description: "Your success is our success. We work as an extension of your team, not just another vendor."
    },
    {
      icon: TrendingUp,
      title: "DATA-OBSESSED",
      description: "Every decision is backed by data. We test, measure, and optimize everything."
    },
    {
      icon: Zap,
      title: "INNOVATION-FIRST",
      description: "Social media changes fast. We stay ahead of trends and platform updates."
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-blue-500">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="asymmetric-grid">
            <div className="bg-white text-black p-8 md:p-12 brutal-border brutal-shadow max-w-4xl mx-auto">
              <h1 className="brutal-text text-4xl md:text-6xl mb-6">
                WE'RE NOT YOUR TYPICAL SMMA
              </h1>
              <p className="text-xl md:text-2xl font-bold mb-8">
                We're the agency that actually cares about your bottom line. 
                No fluff, no vanity metrics, just revenue-generating social media strategies.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <div key={index} className="text-center asymmetric-grid">
                <div className="bg-yellow-400 text-black p-6 brutal-border brutal-shadow">
                  <div className="brutal-text text-3xl md:text-4xl mb-2">{stat.number}</div>
                  <div className="font-bold text-sm">{stat.label}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Our Story */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="asymmetric-grid">
              <div className="bg-pink-500 text-white p-8 brutal-border brutal-shadow">
                <h2 className="brutal-text text-3xl md:text-4xl mb-6">OUR STORY</h2>
                <p className="text-lg font-bold mb-6 leading-relaxed">
                  GrowthLab was born out of frustration. Frustration with agencies that prioritize 
                  pretty posts over profit. Frustration with "social media experts" who couldn't 
                  tie their strategies to actual business results.
                </p>
                <p className="text-lg font-bold mb-6 leading-relaxed">
                  We started GrowthLab with one mission: prove that social media can be a legitimate, 
                  measurable revenue channel when done right.
                </p>
                <p className="text-lg font-bold leading-relaxed">
                  Today, we've helped 50+ businesses transform their social media from a cost center 
                  into their #1 lead generation channel.
                </p>
              </div>
            </div>

            <div className="anti-asymmetric">
              <div className="bg-green-400 text-black p-8 brutal-border brutal-shadow">
                <h3 className="brutal-text text-2xl mb-6">WHAT MAKES US DIFFERENT</h3>
                <ul className="space-y-4 font-bold">
                  <li className="flex items-start gap-3">
                    <span className="bg-black text-white w-6 h-6 brutal-text text-xs flex items-center justify-center mt-1">✓</span>
                    <span>We track revenue attribution, not just engagement</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <span className="bg-black text-white w-6 h-6 brutal-text text-xs flex items-center justify-center mt-1">✓</span>
                    <span>Every campaign is designed with conversion in mind</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <span className="bg-black text-white w-6 h-6 brutal-text text-xs flex items-center justify-center mt-1">✓</span>
                    <span>We provide detailed ROI reporting on every dollar spent</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <span className="bg-black text-white w-6 h-6 brutal-text text-xs flex items-center justify-center mt-1">✓</span>
                    <span>Our team has managed over $2M in ad spend</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <span className="bg-black text-white w-6 h-6 brutal-text text-xs flex items-center justify-center mt-1">✓</span>
                    <span>We guarantee results or your money back</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Values */}
      <section className="py-20 bg-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="bg-yellow-400 text-black p-4 brutal-border brutal-shadow inline-block transform -rotate-1">
              <h2 className="brutal-text text-2xl md:text-3xl">OUR VALUES</h2>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div key={index} className={index % 2 === 0 ? "asymmetric-grid" : "anti-asymmetric"}>
                <div className="bg-white brutal-border brutal-shadow p-6 text-center h-full">
                  <div className="bg-black w-16 h-16 brutal-border brutal-shadow-small mx-auto mb-4 flex items-center justify-center">
                    <value.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="brutal-text text-lg mb-3">{value.title}</h3>
                  <p className="font-bold text-gray-600">{value.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="bg-pink-500 text-white p-4 brutal-border brutal-shadow inline-block transform rotate-1">
              <h2 className="brutal-text text-2xl md:text-3xl">MEET THE TEAM</h2>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {teamMembers.map((member, index) => (
              <div key={member.id} className={index % 2 === 0 ? "asymmetric-grid" : "anti-asymmetric"}>
                <div className="bg-white brutal-border brutal-shadow p-6 text-center">
                  {/* Photo Placeholder */}
                  <div className="bg-gray-200 w-32 h-32 brutal-border brutal-shadow-small mx-auto mb-6 flex items-center justify-center">
                    <span className="brutal-text text-sm text-gray-500">PHOTO</span>
                  </div>

                  <h3 className="brutal-text text-xl mb-2">{member.name}</h3>
                  <div className="bg-blue-500 text-white px-3 py-1 brutal-text text-xs inline-block mb-4">
                    {member.role.toUpperCase()}
                  </div>
                  
                  <p className="font-bold text-gray-600 mb-4 leading-relaxed">
                    {member.bio}
                  </p>

                  {/* Specialties */}
                  <div className="flex flex-wrap gap-2 justify-center">
                    {member.specialties.map((specialty, specialtyIndex) => (
                      <span 
                        key={specialtyIndex}
                        className="bg-gray-100 text-gray-700 px-2 py-1 text-xs font-bold"
                      >
                        {specialty}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-black">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-green-400 text-black p-8 md:p-12 brutal-border brutal-shadow">
            <h2 className="brutal-text text-3xl md:text-4xl mb-6">
              READY TO WORK WITH US?
            </h2>
            <p className="text-xl font-bold mb-8">
              Let's have a conversation about your business goals and how we can help you achieve them through strategic social media marketing.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                to={createPageUrl("Contact")}
                className="bg-pink-500 text-white px-8 py-4 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 inline-flex items-center justify-center"
              >
                BOOK YOUR FREE CALL
                <ArrowUpRight className="w-6 h-6 ml-2" />
              </Link>
              
              <Link 
                to={createPageUrl("CaseStudies")}
                className="bg-white text-black px-8 py-4 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 inline-flex items-center justify-center"
              >
                SEE OUR RESULTS
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
