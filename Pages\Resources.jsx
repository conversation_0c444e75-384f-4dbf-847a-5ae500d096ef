import React, { useState, useEffect } from "react";
import { Resource } from "@/entities/Resource";
import { Download, FileText, Calculator, Video, BookOpen, Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import EmailCaptureModal from "../components/EmailCaptureModal";
import { Link } from 'react-router-dom';
import { createPageUrl } from '@/utils';


export default function Resources() {
  const [resources, setResources] = useState([]);
  const [filteredResources, setFilteredResources] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedType, setSelectedType] = useState("all");
  
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedResource, setSelectedResource] = useState(null);

  const categories = [
    { value: "all", label: "ALL CATEGORIES" },
    { value: "social-media", label: "SOCIAL MEDIA" },
    { value: "advertising", label: "ADVERTISING" },
    { value: "strategy", label: "STRATEGY" },
    { value: "analytics", label: "ANALYTICS" },
    { value: "content", label: "CONTENT" }
  ];

  const types = [
    { value: "all", label: "ALL TYPES" },
    { value: "guide", label: "GUIDES" },
    { value: "template", label: "TEMPLATES" },
    { value: "checklist", label: "CHECKLISTS" },
    { value: "calculator", label: "CALCULATORS" },
    { value: "ebook", label: "EBOOKS" },
    { value: "webinar", label: "WEBINARS" }
  ];

  useEffect(() => {
    loadResources();
  }, []);

  useEffect(() => {
    filterResources();
  }, [resources, searchTerm, selectedCategory, selectedType]);

  const loadResources = async () => {
    try {
      const resourceData = await Resource.list('-created_date');
      setResources(resourceData);
    } catch (error) {
      console.error("Error loading resources:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const filterResources = () => {
    let filtered = resources;

    if (selectedCategory !== "all") {
      filtered = filtered.filter(resource => resource.category === selectedCategory);
    }

    if (selectedType !== "all") {
      filtered = filtered.filter(resource => resource.type === selectedType);
    }

    if (searchTerm) {
      filtered = filtered.filter(resource => 
        resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        resource.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredResources(filtered);
  };

  const getTypeIcon = (type) => {
    const icons = {
      guide: BookOpen,
      template: FileText,
      checklist: FileText,
      calculator: Calculator,
      ebook: BookOpen,
      webinar: Video
    };
    return icons[type] || FileText;
  };

  const getTypeColor = (type) => {
    const colors = {
      guide: "bg-blue-500",
      template: "bg-green-400",
      checklist: "bg-yellow-400",
      calculator: "bg-pink-500",
      ebook: "bg-purple-500",
      webinar: "bg-red-500"
    };
    return colors[type] || "bg-gray-500";
  };
  
  const handleDownloadClick = (resource) => {
    setSelectedResource(resource);
    setIsModalOpen(true);
  }

  return (
    <div className="bg-white min-h-screen">
       {selectedResource && (
        <EmailCaptureModal 
            resource={selectedResource}
            isOpen={isModalOpen}
            onClose={() => {
                setIsModalOpen(false);
                setSelectedResource(null);
                loadResources(); // Refresh resources to show updated download count
            }}
        />
       )}
      {/* Header */}
      <section className="py-20 bg-yellow-400">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-black text-white p-8 brutal-border brutal-shadow inline-block transform -rotate-1 mb-8">
            <h1 className="brutal-text text-4xl md:text-6xl">FREE RESOURCES</h1>
          </div>
          <p className="text-xl md:text-2xl font-bold text-black max-w-3xl mx-auto">
            Download our proven templates, guides, and tools to supercharge your social media marketing. 
            Everything you need to start generating real results.
          </p>
        </div>
      </section>

      {/* Search and Filters */}
      <section className="py-8 bg-white brutal-border border-b-4">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
            {/* Search */}
            <div className="relative md:col-span-2">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                type="text"
                placeholder="Search resources..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 brutal-border brutal-shadow-small"
              />
            </div>

            {/* Category Filter */}
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="brutal-border brutal-shadow-small">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category.value} value={category.value}>
                    {category.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Type Filter */}
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="brutal-border brutal-shadow-small">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                {types.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </section>

      {/* Resources Grid */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div key={i} className="bg-gray-200 brutal-border brutal-shadow p-6 animate-pulse h-80"></div>
              ))}
            </div>
          ) : filteredResources.length > 0 ? (
            <>
              {/* Featured Resources */}
              {filteredResources.filter(r => r.featured).length > 0 && (
                <div className="mb-16">
                  <div className="text-center mb-8">
                    <div className="bg-pink-500 text-white px-6 py-3 brutal-border brutal-shadow inline-block transform rotate-1">
                      <h2 className="brutal-text text-2xl">FEATURED RESOURCES</h2>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {filteredResources.filter(r => r.featured).slice(0, 2).map((resource, index) => {
                      const IconComponent = getTypeIcon(resource.type);
                      return (
                        <div key={resource.id} className={index % 2 === 0 ? "asymmetric-grid" : "anti-asymmetric"}>
                          <div className="bg-white brutal-border brutal-shadow p-8 hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300">
                            {resource.thumbnail_url && (
                              <div className="bg-gray-200 brutal-border brutal-shadow-small mb-6 overflow-hidden">
                                <img 
                                  src={resource.thumbnail_url} 
                                  alt={resource.title}
                                  className="w-full h-40 object-cover"
                                />
                              </div>
                            )}
                            
                            <div className="flex items-center gap-4 mb-4">
                              <div className={`${getTypeColor(resource.type)} w-12 h-12 brutal-border brutal-shadow-small flex items-center justify-center`}>
                                <IconComponent className="w-6 h-6 text-white" />
                              </div>
                              <div>
                                <div className="brutal-text text-xs text-gray-500">
                                  {resource.type.toUpperCase()}
                                </div>
                                <div className="brutal-text text-lg">{resource.title}</div>
                              </div>
                            </div>
                            
                            <p className="font-bold text-gray-800 mb-6">
                              {resource.description}
                            </p>

                            <div className="flex items-center justify-between">
                              <div className="text-sm font-bold text-gray-600">
                                {resource.download_count || 0} downloads
                              </div>
                              <Button
                                onClick={() => handleDownloadClick(resource)}
                                className="bg-pink-500 text-white brutal-border brutal-shadow-small brutal-text hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
                              >
                                <Download className="w-4 h-4 mr-2" />
                                DOWNLOAD
                              </Button>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* All Resources */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredResources.filter(r => !r.featured).map((resource, index) => {
                  const IconComponent = getTypeIcon(resource.type);
                  return (
                    <div key={resource.id} className={index % 2 === 0 ? "asymmetric-grid" : "anti-asymmetric"}>
                      <div className="bg-white brutal-border brutal-shadow p-6 hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300 h-full flex flex-col">
                        {resource.thumbnail_url && (
                          <div className="bg-gray-200 brutal-border brutal-shadow-small mb-4 overflow-hidden">
                            <img 
                              src={resource.thumbnail_url} 
                              alt={resource.title}
                              className="w-full h-32 object-cover"
                            />
                          </div>
                        )}
                        
                        <div className="flex items-center gap-3 mb-3">
                          <div className={`${getTypeColor(resource.type)} w-8 h-8 brutal-border flex items-center justify-center`}>
                            <IconComponent className="w-4 h-4 text-white" />
                          </div>
                          <div className="brutal-text text-xs text-gray-500">
                            {resource.type.toUpperCase()}
                          </div>
                        </div>
                        
                        <h3 className="brutal-text text-lg mb-3 line-clamp-2 flex-grow">
                          {resource.title}
                        </h3>
                        
                        <p className="font-bold text-sm text-gray-800 mb-4 line-clamp-3 flex-grow">
                          {resource.description}
                        </p>

                        <div className="mt-auto">
                          <div className="flex items-center justify-between mb-4">
                            <div className="text-xs font-bold text-gray-600">
                              {resource.download_count || 0} downloads
                            </div>
                            <div className={`${getTypeColor(resource.category)} text-black px-2 py-1 brutal-text text-xs`}>
                              {resource.category?.replace('-', ' ').toUpperCase()}
                            </div>
                          </div>
                          
                          <Button
                            onClick={() => handleDownloadClick(resource)}
                            className="w-full bg-blue-500 text-white brutal-border brutal-shadow-small brutal-text hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
                            size="sm"
                          >
                            <Download className="w-4 h-4 mr-2" />
                            GET INSTANT ACCESS
                          </Button>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </>
          ) : (
            <div className="text-center">
              <div className="bg-gray-100 brutal-border brutal-shadow p-12 max-w-2xl mx-auto">
                <h2 className="brutal-text text-2xl mb-4">NO RESOURCES FOUND</h2>
                <p className="font-bold text-gray-600 mb-6">
                  {searchTerm || selectedCategory !== "all" || selectedType !== "all"
                    ? "No resources match your search criteria. Try adjusting your filters." 
                    : "We're building an amazing resource library for you. Check back soon!"
                  }
                </p>
                {(searchTerm || selectedCategory !== "all" || selectedType !== "all") && (
                  <Button
                    onClick={() => {
                      setSearchTerm("");
                      setSelectedCategory("all");
                      setSelectedType("all");
                    }}
                    className="bg-pink-500 text-white brutal-border brutal-shadow brutal-text hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150"
                  >
                    CLEAR FILTERS
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-black">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-green-400 text-black p-8 md:p-12 brutal-border brutal-shadow">
            <h2 className="brutal-text text-3xl md:text-4xl mb-6">
              WANT CUSTOM STRATEGIES?
            </h2>
            <p className="text-xl font-bold mb-8">
              These free resources are a great start. For personalized strategies and full implementation, let's talk.
            </p>
            <Link to={createPageUrl("Contact")}>
              <Button className="bg-pink-500 text-white px-8 py-4 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150">
                SCHEDULE STRATEGY CALL
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}