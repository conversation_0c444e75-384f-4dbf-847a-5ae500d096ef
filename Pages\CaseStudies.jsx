import React, { useState, useEffect } from "react";
import { CaseStudy } from "@/entities/CaseStudy";
import { ArrowUpRight, TrendingUp, Target, Users, DollarSign, ExternalLink } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { createPageUrl } from "@/utils";

export default function CaseStudies() {
  const [caseStudies, setCaseStudies] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedStudy, setSelectedStudy] = useState(null);

  useEffect(() => {
    loadCaseStudies();
  }, []);

  const loadCaseStudies = async () => {
    try {
      const studies = await CaseStudy.list('-created_date');
      setCaseStudies(studies);
      if (studies.length > 0) {
        setSelectedStudy(studies[0]);
      }
    } catch (error) {
      console.error("Error loading case studies:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const getMetricIcon = (metric) => {
    if (metric.toLowerCase().includes('revenue') || metric.toLowerCase().includes('sales') || metric.toLowerCase().includes('roas')) {
      return DollarSign;
    } else if (metric.toLowerCase().includes('lead') || metric.toLowerCase().includes('conversion')) {
      return Target;
    } else if (metric.toLowerCase().includes('follower') || metric.toLowerCase().includes('engagement')) {
      return Users;
    }
    return TrendingUp;
  };

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-blue-500">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white text-black p-8 brutal-border brutal-shadow inline-block transform rotate-1 mb-8">
            <h1 className="brutal-text text-4xl md:text-6xl">CASE STUDIES</h1>
          </div>
          <p className="text-xl md:text-2xl font-bold text-white max-w-3xl mx-auto">
            Real results from real clients. No fluff, no vanity metrics – just cold, hard revenue numbers.
          </p>
        </div>
      </section>

      {isLoading ? (
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[1, 2, 3].map((i) => (
                <div key={i} className="bg-gray-200 brutal-border brutal-shadow p-8 animate-pulse h-96"></div>
              ))}
            </div>
          </div>
        </section>
      ) : caseStudies.length > 0 ? (
        <>
          {/* Featured Case Study */}
          {selectedStudy && (
            <section className="py-20 bg-white">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                  <div className="asymmetric-grid">
                    <div className="bg-pink-500 text-white p-8 md:p-12 brutal-border brutal-shadow">
                      {selectedStudy.client_logo_url && (
                        <img 
                          src={selectedStudy.client_logo_url} 
                          alt={selectedStudy.client_name}
                          className="h-12 mb-6 filter invert"
                        />
                      )}
                      <h2 className="brutal-text text-3xl md:text-4xl mb-4">
                        {selectedStudy.client_name}
                      </h2>
                      {selectedStudy.industry && (
                        <div className="bg-yellow-400 text-black px-3 py-1 brutal-text text-sm inline-block mb-6">
                          {selectedStudy.industry}
                        </div>
                      )}
                      <h3 className="brutal-text text-xl mb-4">THE CHALLENGE</h3>
                      <p className="font-bold text-lg leading-relaxed mb-6">
                        {selectedStudy.challenge}
                      </p>
                      <h3 className="brutal-text text-xl mb-4">OUR SOLUTION</h3>
                      <p className="font-bold text-lg leading-relaxed">
                        {selectedStudy.solution}
                      </p>
                    </div>
                  </div>

                  <div className="anti-asymmetric">
                    <div className="bg-white brutal-border brutal-shadow p-8">
                      <h3 className="brutal-text text-2xl mb-8 text-center">THE RESULTS</h3>
                      
                      <div className="grid grid-cols-1 gap-6 mb-8">
                        {selectedStudy.results?.map((result, index) => {
                          const IconComponent = getMetricIcon(result.metric);
                          return (
                            <div key={index} className="bg-green-400 text-black p-6 brutal-border brutal-shadow-small">
                              <div className="flex items-center gap-4">
                                <div className="bg-black text-white w-12 h-12 brutal-border flex items-center justify-center">
                                  <IconComponent className="w-6 h-6" />
                                </div>
                                <div>
                                  <div className="brutal-text text-2xl">{result.value}</div>
                                  <div className="font-bold text-sm">{result.metric}</div>
                                  {result.improvement && (
                                    <div className="font-bold text-xs opacity-80">{result.improvement}</div>
                                  )}
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>

                      {selectedStudy.services_used && selectedStudy.services_used.length > 0 && (
                        <div className="mb-6">
                          <h4 className="brutal-text text-sm mb-3">SERVICES USED</h4>
                          <div className="flex flex-wrap gap-2">
                            {selectedStudy.services_used.map((service, i) => (
                              <span key={i} className="bg-blue-500 text-white px-3 py-1 brutal-text text-xs">
                                {service}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                      {selectedStudy.testimonial && (
                        <div className="bg-yellow-400 text-black p-6 brutal-border brutal-shadow-small">
                          <p className="font-bold mb-4">"{selectedStudy.testimonial}"</p>
                          {selectedStudy.testimonial_author && (
                            <p className="brutal-text text-sm">- {selectedStudy.testimonial_author}</p>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </section>
          )}

          {/* All Case Studies Grid */}
          <section className="py-20 bg-gray-100">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-16">
                <div className="bg-black text-white p-6 brutal-border brutal-shadow inline-block transform -rotate-1">
                  <h2 className="brutal-text text-3xl">ALL CASE STUDIES</h2>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {caseStudies.map((study, index) => (
                  <div 
                    key={study.id} 
                    className={`cursor-pointer ${index % 2 === 0 ? 'asymmetric-grid' : 'anti-asymmetric'}`}
                    onClick={() => setSelectedStudy(study)}
                  >
                    <div className={`bg-white brutal-border brutal-shadow p-6 hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300 h-full ${selectedStudy?.id === study.id ? 'ring-4 ring-pink-500' : ''}`}>
                      {study.featured_image_url && (
                        <div className="bg-gray-200 brutal-border brutal-shadow-small mb-4 overflow-hidden">
                          <img 
                            src={study.featured_image_url} 
                            alt={study.client_name}
                            className="w-full h-32 object-cover"
                          />
                        </div>
                      )}
                      
                      <h3 className="brutal-text text-lg mb-2">{study.client_name}</h3>
                      
                      {study.industry && (
                        <div className="bg-yellow-400 text-black px-2 py-1 brutal-text text-xs inline-block mb-4">
                          {study.industry}
                        </div>
                      )}
                      
                      <p className="font-bold text-sm text-gray-800 mb-4 line-clamp-3">
                        {study.challenge}
                      </p>

                      {study.results && study.results.length > 0 && (
                        <div className="space-y-2">
                          {study.results.slice(0, 2).map((result, i) => (
                            <div key={i} className="flex items-center justify-between text-sm">
                              <span className="font-bold">{result.metric}:</span>
                              <span className="brutal-text text-green-600">{result.value}</span>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>
        </>
      ) : (
        <section className="py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="bg-gray-100 brutal-border brutal-shadow p-12">
              <h2 className="brutal-text text-3xl mb-6">CASE STUDIES COMING SOON</h2>
              <p className="text-xl font-bold text-gray-600 mb-8">
                We're busy generating results for our clients. 
                Detailed case studies with full metrics are being prepared.
              </p>
              <Link to={createPageUrl("Contact")}>
                <Button className="bg-pink-500 text-white px-8 py-4 brutal-border brutal-shadow brutal-text hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150">
                  BECOME OUR NEXT SUCCESS STORY
                  <ArrowUpRight className="w-5 h-5 ml-2" />
                </Button>
              </Link>
            </div>
          </div>
        </section>
      )}

      {/* CTA Section */}
      <section className="py-20 bg-black">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-green-400 text-black p-8 md:p-12 brutal-border brutal-shadow asymmetric-grid">
            <h2 className="brutal-text text-3xl md:text-4xl mb-6">
              WANT RESULTS LIKE THESE?
            </h2>
            <p className="text-xl font-bold mb-8">
              Stop watching your competitors win on social media. 
              Let's build you a system that generates real revenue.
            </p>
            <Link to={createPageUrl("Contact")}>
              <Button className="bg-pink-500 text-white px-8 py-4 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150">
                SCHEDULE YOUR STRATEGY CALL
                <ArrowUpRight className="w-6 h-6 ml-2" />
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}