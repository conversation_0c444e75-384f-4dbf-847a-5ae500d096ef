import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { CaseStudy } from "@/entities/CaseStudy";
import { ArrowLeft, Download, Play, TrendingUp, Target, Users, DollarSign, ExternalLink } from "lucide-react";
import { Button } from "@/components/ui/button";
import { createPageUrl } from "@/utils";

export default function CaseStudyDetail() {
  const { id } = useParams();
  const [caseStudy, setCaseStudy] = useState(null);
  const [relatedStudies, setRelatedStudies] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [notFound, setNotFound] = useState(false);

  useEffect(() => {
    loadCaseStudy();
    loadRelatedStudies();
  }, [id]);

  const loadCaseStudy = async () => {
    try {
      // Since we don't have a direct get method, we'll filter by ID
      const studies = await CaseStudy.list();
      const study = studies.find(s => s.id === id);
      if (study) {
        setCaseStudy(study);
      } else {
        setNotFound(true);
      }
    } catch (error) {
      console.error("Error loading case study:", error);
      setNotFound(true);
    } finally {
      setIsLoading(false);
    }
  };

  const loadRelatedStudies = async () => {
    try {
      const studies = await CaseStudy.list('-created_date', 3);
      setRelatedStudies(studies);
    } catch (error) {
      console.error("Error loading related studies:", error);
    }
  };

  const getMetricIcon = (metric) => {
    if (metric.toLowerCase().includes('revenue') || metric.toLowerCase().includes('sales') || metric.toLowerCase().includes('roas')) {
      return DollarSign;
    } else if (metric.toLowerCase().includes('lead') || metric.toLowerCase().includes('conversion')) {
      return Target;
    } else if (metric.toLowerCase().includes('follower') || metric.toLowerCase().includes('engagement')) {
      return Users;
    }
    return TrendingUp;
  };

  const downloadCaseStudy = () => {
    // Generate PDF download (simplified version)
    const content = `
CASE STUDY: ${caseStudy.client_name}

INDUSTRY: ${caseStudy.industry}

THE CHALLENGE:
${caseStudy.challenge}

OUR SOLUTION:
${caseStudy.solution}

RESULTS:
${caseStudy.results?.map(r => `${r.metric}: ${r.value} ${r.improvement}`).join('\n')}

TESTIMONIAL:
"${caseStudy.testimonial}"
- ${caseStudy.testimonial_author}
    `;
    
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${caseStudy.client_name.replace(/\s+/g, '_')}_Case_Study.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-white py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gray-200 brutal-border brutal-shadow p-8 animate-pulse">
            <div className="h-8 bg-gray-300 mb-4"></div>
            <div className="h-4 bg-gray-300 mb-2"></div>
            <div className="h-4 bg-gray-300 w-3/4"></div>
          </div>
        </div>
      </div>
    );
  }

  if (notFound) {
    return (
      <div className="min-h-screen bg-white py-20 flex items-center justify-center">
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-red-100 text-black p-8 brutal-border brutal-shadow">
            <h1 className="brutal-text text-4xl mb-6">CASE STUDY NOT FOUND</h1>
            <p className="text-xl font-bold mb-8">
              The case study you're looking for doesn't exist.
            </p>
            <Link to={createPageUrl("CaseStudies")}>
              <Button className="bg-pink-500 text-white brutal-border brutal-shadow brutal-text hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150">
                BACK TO CASE STUDIES
                <ArrowLeft className="w-5 h-5 ml-2" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white min-h-screen">
      {/* Header */}
      <section className="py-12 bg-blue-500">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Link 
            to={createPageUrl("CaseStudies")}
            className="inline-flex items-center gap-2 mb-8 bg-white text-black brutal-border brutal-shadow-small px-4 py-2 hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
          >
            <ArrowLeft className="w-4 h-4" />
            <span className="font-bold">BACK TO CASE STUDIES</span>
          </Link>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="bg-white text-black p-8 brutal-border brutal-shadow">
                {caseStudy.client_logo_url && (
                  <img 
                    src={caseStudy.client_logo_url} 
                    alt={caseStudy.client_name}
                    className="h-12 mb-6"
                  />
                )}
                <h1 className="brutal-text text-4xl md:text-5xl mb-4">
                  {caseStudy.client_name}
                </h1>
                {caseStudy.industry && (
                  <div className="bg-yellow-400 text-black px-4 py-2 brutal-text text-lg inline-block mb-6">
                    {caseStudy.industry}
                  </div>
                )}
                <p className="text-xl font-bold text-gray-700">
                  How we helped {caseStudy.client_name} achieve extraordinary results through strategic social media marketing.
                </p>
              </div>
            </div>

            <div>
              <div className="bg-pink-500 text-white p-8 brutal-border brutal-shadow">
                <h2 className="brutal-text text-2xl mb-6">QUICK RESULTS</h2>
                <div className="grid grid-cols-2 gap-4">
                  {caseStudy.results?.slice(0, 4).map((result, index) => {
                    const IconComponent = getMetricIcon(result.metric);
                    return (
                      <div key={index} className="text-center">
                        <div className="bg-white text-black w-12 h-12 brutal-border mx-auto mb-2 flex items-center justify-center">
                          <IconComponent className="w-6 h-6" />
                        </div>
                        <div className="brutal-text text-2xl">{result.value}</div>
                        <div className="text-sm font-bold opacity-90">{result.metric}</div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Image */}
      {caseStudy.featured_image_url && (
        <section className="py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-gray-200 brutal-border brutal-shadow overflow-hidden">
              <img 
                src={caseStudy.featured_image_url} 
                alt={caseStudy.client_name}
                className="w-full h-64 md:h-96 object-cover"
              />
            </div>
          </div>
        </section>
      )}

      {/* Challenge & Solution */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div className="asymmetric-grid">
              <div className="bg-yellow-400 text-black p-8 brutal-border brutal-shadow h-full">
                <h2 className="brutal-text text-3xl mb-6">THE CHALLENGE</h2>
                <p className="font-bold text-lg leading-relaxed">
                  {caseStudy.challenge}
                </p>
              </div>
            </div>

            <div className="anti-asymmetric">
              <div className="bg-green-400 text-black p-8 brutal-border brutal-shadow h-full">
                <h2 className="brutal-text text-3xl mb-6">OUR SOLUTION</h2>
                <p className="font-bold text-lg leading-relaxed">
                  {caseStudy.solution}
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Detailed Results */}
      <section className="py-20 bg-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="bg-black text-white p-6 brutal-border brutal-shadow inline-block transform rotate-1">
              <h2 className="brutal-text text-3xl">DETAILED RESULTS</h2>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {caseStudy.results?.map((result, index) => {
              const IconComponent = getMetricIcon(result.metric);
              return (
                <div key={index} className={index % 2 === 0 ? "asymmetric-grid" : "anti-asymmetric"}>
                  <div className="bg-white brutal-border brutal-shadow p-8 text-center h-full">
                    <div className="bg-blue-500 text-white w-16 h-16 brutal-border brutal-shadow-small mx-auto mb-6 flex items-center justify-center">
                      <IconComponent className="w-8 h-8" />
                    </div>
                    <div className="brutal-text text-4xl mb-2 text-pink-500">{result.value}</div>
                    <div className="brutal-text text-lg mb-2">{result.metric}</div>
                    {result.improvement && (
                      <div className="font-bold text-sm text-gray-600">{result.improvement}</div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {caseStudy.services_used && caseStudy.services_used.length > 0 && (
            <div className="mt-16 text-center">
              <div className="bg-white brutal-border brutal-shadow p-8 max-w-3xl mx-auto">
                <h3 className="brutal-text text-2xl mb-6">SERVICES UTILIZED</h3>
                <div className="flex flex-wrap justify-center gap-4">
                  {caseStudy.services_used.map((service, i) => (
                    <div key={i} className="bg-pink-500 text-white px-6 py-3 brutal-border brutal-shadow-small">
                      <span className="brutal-text text-sm">{service}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Client Testimonial */}
      {caseStudy.testimonial && (
        <section className="py-20 bg-black">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="bg-yellow-400 text-black p-8 md:p-12 brutal-border brutal-shadow">
              <h2 className="brutal-text text-2xl mb-8">WHAT OUR CLIENT SAYS</h2>
              <blockquote className="text-2xl md:text-3xl font-bold leading-relaxed mb-8">
                "{caseStudy.testimonial}"
              </blockquote>
              {caseStudy.testimonial_author && (
                <cite className="brutal-text text-lg">
                  - {caseStudy.testimonial_author}
                </cite>
              )}
            </div>
          </div>
        </section>
      )}

      {/* Download & CTA */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-green-400 text-black p-8 brutal-border brutal-shadow">
            <h2 className="brutal-text text-3xl mb-6">
              WANT RESULTS LIKE THESE?
            </h2>
            <p className="text-xl font-bold mb-8">
              Download the full case study or schedule a call to see how we can replicate these results for your business.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                onClick={downloadCaseStudy}
                className="bg-black text-white px-8 py-4 brutal-border brutal-shadow brutal-text hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150"
              >
                <Download className="w-5 h-5 mr-2" />
                DOWNLOAD CASE STUDY
              </Button>
              <Link to={createPageUrl("Contact")}>
                <Button className="bg-pink-500 text-white px-8 py-4 brutal-border brutal-shadow brutal-text hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150">
                  GET YOUR FREE STRATEGY CALL
                  <ExternalLink className="w-5 h-5 ml-2" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Related Case Studies */}
      {relatedStudies.length > 0 && (
        <section className="py-16 bg-gray-100">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <div className="bg-blue-500 text-white p-6 brutal-border brutal-shadow inline-block transform -rotate-1">
                <h2 className="brutal-text text-2xl">MORE SUCCESS STORIES</h2>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {relatedStudies.filter(s => s.id !== caseStudy.id).slice(0, 3).map((study, index) => (
                <Link key={study.id} to={createPageUrl(`CaseStudyDetail?id=${study.id}`)}>
                  <div className={`cursor-pointer ${index % 2 === 0 ? 'asymmetric-grid' : 'anti-asymmetric'}`}>
                    <div className="bg-white brutal-border brutal-shadow p-6 hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300 h-full">
                      {study.featured_image_url && (
                        <div className="bg-gray-200 brutal-border brutal-shadow-small mb-4 overflow-hidden">
                          <img 
                            src={study.featured_image_url} 
                            alt={study.client_name}
                            className="w-full h-32 object-cover"
                          />
                        </div>
                      )}
                      
                      <h3 className="brutal-text text-lg mb-2">{study.client_name}</h3>
                      
                      {study.industry && (
                        <div className="bg-yellow-400 text-black px-2 py-1 brutal-text text-xs inline-block mb-4">
                          {study.industry}
                        </div>
                      )}
                      
                      <p className="font-bold text-sm text-gray-800 mb-4 line-clamp-3">
                        {study.challenge}
                      </p>

                      {study.results && study.results.length > 0 && (
                        <div className="space-y-2">
                          {study.results.slice(0, 2).map((result, i) => (
                            <div key={i} className="flex items-center justify-between text-sm">
                              <span className="font-bold">{result.metric}:</span>
                              <span className="brutal-text text-green-600">{result.value}</span>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </section>
      )}
    </div>
  );
}