{"name": "TeamMember", "type": "object", "properties": {"name": {"type": "string", "description": "Team member's full name"}, "role": {"type": "string", "description": "Job title/role"}, "bio": {"type": "string", "description": "Short biography"}, "photo_url": {"type": "string", "description": "Profile photo URL"}, "email": {"type": "string", "description": "Professional email"}, "linkedin_url": {"type": "string", "description": "LinkedIn profile URL"}, "specialties": {"type": "array", "items": {"type": "string"}, "description": "Areas of expertise"}, "order_index": {"type": "number", "description": "Display order on team page"}}, "required": ["name", "role", "bio"]}