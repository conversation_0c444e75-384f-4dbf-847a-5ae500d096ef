{"name": "BlogPost", "type": "object", "properties": {"title": {"type": "string", "description": "Blog post title"}, "slug": {"type": "string", "description": "URL-friendly version of title"}, "excerpt": {"type": "string", "description": "Short description/preview"}, "content": {"type": "string", "description": "Full blog post content"}, "featured_image_url": {"type": "string", "description": "Main blog post image"}, "category": {"type": "string", "enum": ["strategy", "tips", "case-studies", "industry-news", "tutorials"], "description": "Blog post category"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Blog post tags"}, "read_time": {"type": "number", "description": "Estimated read time in minutes"}, "published": {"type": "boolean", "default": true, "description": "Whether the post is published"}}, "required": ["title", "content", "category"]}