import React, { useState } from "react";
import { SendEmail } from "@/integrations/Core";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Mail, Phone, MapPin, Clock, CheckCircle, Zap } from "lucide-react";

export default function Contact() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    company: "",
    phone: "",
    service: "",
    budget: "",
    message: "",
    timeline: ""
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const services = [
    "Content Creation",
    "Paid Advertising", 
    "Community Management",
    "Analytics & Reporting",
    "Full Service Package",
    "Not Sure - Need Consultation"
  ];

  const budgets = [
    "Under $3,000/month",
    "$3,000 - $5,000/month", 
    "$5,000 - $10,000/month",
    "$10,000 - $25,000/month",
    "$25,000+/month",
    "Let's discuss"
  ];

  const timelines = [
    "ASAP - Need results yesterday",
    "Within 30 days",
    "1-3 months",
    "3-6 months",
    "Just exploring options"
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const emailBody = `
NEW LEAD FROM GROWTHLAB WEBSITE

Name: ${formData.name}
Email: ${formData.email}
Company: ${formData.company}
Phone: ${formData.phone}
Service Interest: ${formData.service}
Budget: ${formData.budget}
Timeline: ${formData.timeline}

Message:
${formData.message}

---
Submitted from: ${window.location.href}
Time: ${new Date().toLocaleString()}
      `;

      await SendEmail({
        to: "<EMAIL>",
        subject: `New Lead: ${formData.name} - ${formData.company}`,
        body: emailBody,
        from_name: "GrowthLab Website"
      });

      setIsSubmitted(true);
    } catch (error) {
      console.error("Error sending email:", error);
      alert("There was an error sending your message. Please try again or contact us directly.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-green-400 flex items-center justify-center py-20">
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white text-black p-8 md:p-12 brutal-border brutal-shadow">
            <div className="bg-green-400 w-20 h-20 brutal-border brutal-shadow mx-auto mb-8 flex items-center justify-center">
              <CheckCircle className="w-10 h-10 text-black" />
            </div>
            <h1 className="brutal-text text-4xl mb-6">MESSAGE RECEIVED!</h1>
            <p className="text-xl font-bold mb-8">
              Thanks for reaching out! We'll review your information and get back to you within 24 hours with a custom strategy proposal.
            </p>
            <p className="font-bold">
              Keep an eye on your inbox - we're about to show you exactly how to turn your social media into a revenue machine.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-pink-500">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white text-black p-8 brutal-border brutal-shadow inline-block transform -rotate-1 mb-8">
            <h1 className="brutal-text text-4xl md:text-6xl">LET'S TALK</h1>
          </div>
          <p className="text-xl md:text-2xl font-bold text-white max-w-3xl mx-auto">
            Ready to turn your social media into a revenue-generating machine? 
            Let's build a custom strategy for your business.
          </p>
        </div>
      </section>

      <div className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Contact Form */}
            <div className="lg:col-span-2">
              <div className="bg-white brutal-border brutal-shadow p-8">
                <h2 className="brutal-text text-3xl mb-8">GET YOUR FREE STRATEGY CALL</h2>
                
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="brutal-text text-sm mb-2 block">YOUR NAME *</label>
                      <Input
                        type="text"
                        required
                        value={formData.name}
                        onChange={(e) => handleInputChange("name", e.target.value)}
                        className="brutal-border brutal-shadow-small"
                        placeholder="Enter your full name"
                      />
                    </div>
                    
                    <div>
                      <label className="brutal-text text-sm mb-2 block">EMAIL *</label>
                      <Input
                        type="email"
                        required
                        value={formData.email}
                        onChange={(e) => handleInputChange("email", e.target.value)}
                        className="brutal-border brutal-shadow-small"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="brutal-text text-sm mb-2 block">COMPANY *</label>
                      <Input
                        type="text"
                        required
                        value={formData.company}
                        onChange={(e) => handleInputChange("company", e.target.value)}
                        className="brutal-border brutal-shadow-small"
                        placeholder="Your company name"
                      />
                    </div>
                    
                    <div>
                      <label className="brutal-text text-sm mb-2 block">PHONE</label>
                      <Input
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => handleInputChange("phone", e.target.value)}
                        className="brutal-border brutal-shadow-small"
                        placeholder="+****************"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="brutal-text text-sm mb-2 block">SERVICE INTEREST *</label>
                    <Select value={formData.service} onValueChange={(value) => handleInputChange("service", value)}>
                      <SelectTrigger className="brutal-border brutal-shadow-small">
                        <SelectValue placeholder="What service are you interested in?" />
                      </SelectTrigger>
                      <SelectContent>
                        {services.map((service) => (
                          <SelectItem key={service} value={service}>{service}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="brutal-text text-sm mb-2 block">BUDGET RANGE</label>
                      <Select value={formData.budget} onValueChange={(value) => handleInputChange("budget", value)}>
                        <SelectTrigger className="brutal-border brutal-shadow-small">
                          <SelectValue placeholder="Select your budget" />
                        </SelectTrigger>
                        <SelectContent>
                          {budgets.map((budget) => (
                            <SelectItem key={budget} value={budget}>{budget}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <label className="brutal-text text-sm mb-2 block">TIMELINE</label>
                      <Select value={formData.timeline} onValueChange={(value) => handleInputChange("timeline", value)}>
                        <SelectTrigger className="brutal-border brutal-shadow-small">
                          <SelectValue placeholder="When do you want to start?" />
                        </SelectTrigger>
                        <SelectContent>
                          {timelines.map((timeline) => (
                            <SelectItem key={timeline} value={timeline}>{timeline}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div>
                    <label className="brutal-text text-sm mb-2 block">TELL US ABOUT YOUR GOALS</label>
                    <Textarea
                      value={formData.message}
                      onChange={(e) => handleInputChange("message", e.target.value)}
                      className="brutal-border brutal-shadow-small h-32"
                      placeholder="What are your current challenges? What results are you looking for? Any specific questions?"
                    />
                  </div>

                  <Button
                    type="submit"
                    disabled={isSubmitting || !formData.name || !formData.email || !formData.company || !formData.service}
                    className="w-full bg-pink-500 text-white py-6 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? (
                      <>SENDING MESSAGE...</>
                    ) : (
                      <>
                        GET MY FREE STRATEGY CALL
                        <Zap className="w-6 h-6 ml-2" fill="currentColor" />
                      </>
                    )}
                  </Button>
                </form>
              </div>
            </div>

            {/* Contact Info */}
            <div className="space-y-8">
              {/* Contact Details */}
              <div className="bg-yellow-400 text-black p-8 brutal-border brutal-shadow asymmetric-grid">
                <h3 className="brutal-text text-2xl mb-6">CONTACT INFO</h3>
                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <div className="bg-black text-white p-3 brutal-border brutal-shadow-small">
                      <Mail className="w-5 h-5" />
                    </div>
                    <div>
                      <div className="brutal-text text-sm">EMAIL</div>
                      <div className="font-bold"><EMAIL></div>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-4">
                    <div className="bg-black text-white p-3 brutal-border brutal-shadow-small">
                      <Phone className="w-5 h-5" />
                    </div>
                    <div>
                      <div className="brutal-text text-sm">PHONE</div>
                      <div className="font-bold">+****************</div>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="bg-black text-white p-3 brutal-border brutal-shadow-small">
                      <MapPin className="w-5 h-5" />
                    </div>
                    <div>
                      <div className="brutal-text text-sm">ADDRESS</div>
                      <div className="font-bold">123 Digital Street<br />Suite 100<br />Marketing City, MC 12345</div>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="bg-black text-white p-3 brutal-border brutal-shadow-small">
                      <Clock className="w-5 h-5" />
                    </div>
                    <div>
                      <div className="brutal-text text-sm">HOURS</div>
                      <div className="font-bold">Mon-Fri: 9AM-6PM EST<br />Weekends: By appointment</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Response Time */}
              <div className="bg-green-400 text-black p-6 brutal-border brutal-shadow anti-asymmetric">
                <h3 className="brutal-text text-xl mb-4">RESPONSE TIME</h3>
                <p className="font-bold">
                  We respond to all inquiries within 24 hours. 
                  Need something urgent? Call us directly.
                </p>
              </div>

              {/* What Happens Next */}
              <div className="bg-blue-500 text-white p-6 brutal-border brutal-shadow">
                <h3 className="brutal-text text-xl mb-4">WHAT HAPPENS NEXT?</h3>
                <div className="space-y-3 text-sm font-bold">
                  <div className="flex items-start gap-3">
                    <div className="bg-white text-black w-6 h-6 brutal-border text-xs flex items-center justify-center mt-1">1</div>
                    <p>We review your info and prepare a custom strategy</p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="bg-white text-black w-6 h-6 brutal-border text-xs flex items-center justify-center mt-1">2</div>
                    <p>We schedule a 30-minute strategy call</p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="bg-white text-black w-6 h-6 brutal-border text-xs flex items-center justify-center mt-1">3</div>
                    <p>We show you exactly how to generate revenue from social media</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}