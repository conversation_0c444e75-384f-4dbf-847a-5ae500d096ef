import React from 'react';
import { Link } from 'react-router-dom';
import { createPageUrl } from '@/utils';
import { Search, PenTool, BarChart, Flag, ArrowUpRight } from 'lucide-react';

export default function Process() {
  const processSteps = [
    {
      step: "01",
      title: "DISCOVERY & AUDIT",
      description: "We start with a deep-dive audit of your current social presence, target audience, and competitors. We listen to your goals and establish clear, measurable KPIs.",
      icon: Search,
      color: "bg-blue-500",
      bgColor: "bg-blue-100"
    },
    {
      step: "02",
      title: "STRATEGY & ONBOARDING",
      description: "We build a custom, data-driven strategy and content calendar. During onboarding, we get all necessary access and set up communication channels for a seamless partnership.",
      icon: PenTool,
      color: "bg-pink-500",
      bgColor: "bg-pink-100"
    },
    {
      step: "03",
      title: "EXECUTION & CREATION",
      description: "Our team gets to work creating thumb-stopping content, launching targeted ad campaigns, and engaging with your community. You focus on your business while we handle the growth.",
      icon: Bar<PERSON><PERSON>,
      color: "bg-green-400",
      bgColor: "bg-green-100"
    },
    {
      step: "04",
      title: "REPORTING & OPTIMIZATION",
      description: "You receive weekly reports that cut through the vanity metrics and show real ROI. We continuously analyze data to optimize campaigns and maximize your results.",
      icon: Flag,
      color: "bg-yellow-400",
      bgColor: "bg-yellow-100"
    }
  ];

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-purple-500">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white text-black p-8 brutal-border brutal-shadow inline-block transform -rotate-1 mb-8">
            <h1 className="brutal-text text-4xl md:text-6xl">HOW IT WORKS</h1>
          </div>
          <p className="text-xl md:text-2xl font-bold text-white max-w-3xl mx-auto">
            Our proven 4-step process designed for one thing: turning your social media into a revenue-generating machine.
          </p>
        </div>
      </section>

      {/* Process Steps */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="relative">
            {/* Dashed line for desktop */}
            <div className="hidden lg:block absolute top-1/2 left-0 w-full h-1 bg-black border-t-4 border-b-4 border-black border-dotted transform -translate-y-1/2"></div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 relative">
              {processSteps.map((item, index) => (
                <div key={index} className={index % 2 === 0 ? "asymmetric-grid" : "anti-asymmetric"}>
                  <div className={`bg-white text-black p-6 brutal-border brutal-shadow hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300 h-full`}>
                    <div className="flex items-center justify-between mb-4">
                      <div className={`${item.color} text-white w-16 h-16 brutal-border brutal-shadow-small flex items-center justify-center`}>
                        <span className="brutal-text text-2xl">{item.step}</span>
                      </div>
                      <div className={`${item.color} text-white p-2 brutal-border`}>
                        <item.icon className="w-6 h-6" />
                      </div>
                    </div>
                    <h3 className="brutal-text text-xl mb-3">{item.title}</h3>
                    <p className="font-bold text-gray-800">{item.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Visual Flow Section */}
       <section className="py-20 bg-gray-100">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="bg-black text-white p-6 brutal-border brutal-shadow inline-block transform rotate-1 mb-12">
              <h2 className="brutal-text text-3xl md:text-4xl">OUR WORKFLOW</h2>
            </div>
            <div className="flex flex-col md:flex-row items-center justify-center gap-4 text-black brutal-text">
                <div className="bg-blue-500 p-4 brutal-border brutal-shadow">DISCOVERY</div>
                <div className="text-2xl font-black">&rarr;</div>
                <div className="bg-pink-500 p-4 brutal-border brutal-shadow">STRATEGY</div>
                <div className="text-2xl font-black">&rarr;</div>
                <div className="bg-green-400 p-4 brutal-border brutal-shadow">EXECUTION</div>
                <div className="text-2xl font-black">&rarr;</div>
                <div className="bg-yellow-400 p-4 brutal-border brutal-shadow">OPTIMIZATION</div>
            </div>
       </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-yellow-400 text-black p-8 md:p-12 brutal-border brutal-shadow">
            <h2 className="brutal-text text-3xl md:text-4xl mb-6">
              READY TO START STEP 1?
            </h2>
            <p className="text-xl font-bold mb-8">
              Your journey to social media ROI starts with a free, no-obligation strategy call. Let's build your growth plan.
            </p>
            <Link 
              to={createPageUrl("Contact")}
              className="bg-pink-500 text-white px-8 py-4 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 inline-flex items-center"
            >
              BOOK YOUR FREE DISCOVERY CALL
              <ArrowUpRight className="w-6 h-6 ml-2" />
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
