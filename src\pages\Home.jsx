import React from "react";
import { Link } from "react-router-dom";
import { createPageUrl } from "@/utils";
import { ArrowUpRight, TrendingUp, Target, Zap, Users, BarChart3, MessageCircle } from "lucide-react";

export default function Home() {
  const services = [
    {
      icon: MessageCircle,
      title: "CONTENT CREATION",
      description: "Viral content that stops the scroll and converts followers into customers."
    },
    {
      icon: Target,
      title: "PAID ADVERTISING",
      description: "Facebook & Instagram ads that generate 3x+ ROAS consistently."
    },
    {
      icon: Users,
      title: "COMMUNITY MANAGEMENT",
      description: "Build engaged communities that become your biggest brand advocates."
    },
    {
      icon: BarChart3,
      title: "ANALYTICS & REPORTING",
      description: "Data-driven insights that optimize every dollar you spend."
    }
  ];

  const results = [
    { metric: "150%", label: "AVG LEAD INCREASE" },
    { metric: "3.2X", label: "AVERAGE ROAS" },
    { metric: "500K+", label: "FOLLOWERS GROWN" },
    { metric: "50+", label: "BRANDS SCALED" }
  ];

  const clients = [
    "TECHCORP", "FASHIONBRAND", "FITNESSSTUDIO", "FOODCHAIN", "BEAUTYCO", "STARTUPX"
  ];

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="relative min-h-[80vh] flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-yellow-400 asymmetric-grid">
          <div className="absolute inset-0 bg-gradient-to-br from-yellow-400 to-yellow-300"></div>
        </div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="anti-asymmetric">
            <div className="bg-white p-8 md:p-12 brutal-border brutal-shadow max-w-4xl mx-auto">
              <h1 className="brutal-text text-4xl md:text-7xl text-black mb-6 leading-none">
                TURN SOCIAL MEDIA INTO A
                <span className="bg-pink-500 text-white px-4 py-2 inline-block mt-4 transform rotate-1">
                  MONEY MACHINE
                </span>
              </h1>
              
              <p className="text-xl md:text-2xl font-bold text-black mb-8 max-w-2xl mx-auto">
                We don't just post pretty pictures. We build social media strategies that generate real revenue for serious businesses.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Link 
                  to={createPageUrl("Contact")} 
                  className="w-full sm:w-auto bg-blue-500 text-white px-8 py-4 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 inline-flex items-center justify-center"
                >
                  GET FREE STRATEGY CALL
                  <ArrowUpRight className="w-6 h-6 ml-2" />
                </Link>
                
                <Link 
                  to={createPageUrl("CaseStudies")} 
                  className="w-full sm:w-auto bg-white text-black px-8 py-4 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 inline-flex items-center justify-center"
                >
                  VIEW OUR RESULTS
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Results Banner */}
      <section className="py-16 bg-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {results.map((result, index) => (
              <div key={index} className="text-center asymmetric-grid">
                <div className="bg-green-400 text-black p-6 brutal-border brutal-shadow">
                  <div className="brutal-text text-3xl md:text-4xl mb-2">{result.metric}</div>
                  <div className="font-bold text-sm">{result.label}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="bg-pink-500 text-white p-6 brutal-border brutal-shadow inline-block transform -rotate-1 mb-8">
              <h2 className="brutal-text text-3xl md:text-5xl">OUR SERVICES</h2>
            </div>
            <p className="text-xl font-bold text-black max-w-2xl mx-auto">
              We don't do everything. We do these 4 things better than anyone else.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {services.map((service, index) => (
              <div key={index} className={index % 2 === 0 ? "asymmetric-grid" : "anti-asymmetric"}>
                <div className="bg-white brutal-border brutal-shadow p-8 hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300 h-full">
                  <div className="bg-yellow-400 w-16 h-16 brutal-border brutal-shadow-small flex items-center justify-center mb-6">
                    <service.icon className="w-8 h-8 text-black" />
                  </div>
                  <h3 className="brutal-text text-xl mb-4">{service.title}</h3>
                  <p className="font-bold text-gray-800">{service.description}</p>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link 
              to={createPageUrl("Services")}
              className="bg-blue-500 text-white px-8 py-4 brutal-border brutal-shadow brutal-text hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 inline-flex items-center"
            >
              VIEW ALL SERVICES
              <ArrowUpRight className="w-5 h-5 ml-2" />
            </Link>
          </div>
        </div>
      </section>

      {/* Social Proof */}
      <section className="py-16 bg-yellow-400">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="brutal-text text-2xl md:text-3xl text-black mb-8">
            TRUSTED BY GROWING BRANDS
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
            {clients.map((client, index) => (
              <div key={index} className="bg-black text-white p-4 brutal-border brutal-shadow-small">
                <div className="brutal-text text-sm">{client}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-black">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white p-8 md:p-12 brutal-border brutal-shadow asymmetric-grid">
            <h2 className="brutal-text text-3xl md:text-5xl text-black mb-6">
              READY TO DOMINATE SOCIAL MEDIA?
            </h2>
            <p className="text-xl font-bold text-black mb-8">
              Stop wasting time on social media that doesn't generate revenue. 
              Let's build you a system that actually works.
            </p>
            <Link 
              to={createPageUrl("Contact")}
              className="bg-pink-500 text-white px-8 py-4 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 inline-flex items-center"
            >
              BOOK YOUR FREE CALL NOW
              <Zap className="w-6 h-6 ml-2" fill="currentColor" />
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
