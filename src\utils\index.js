// Utility function to create page URLs
export const createPageUrl = (pageName) => {
  const pageRoutes = {
    'Home': '/',
    'About': '/about',
    'Services': '/services',
    'Process': '/process',
    'CaseStudies': '/case-studies',
    'Testimonials': '/testimonials',
    'Resources': '/resources',
    'Tools': '/tools',
    'Blog': '/blog',
    'FAQ': '/faq',
    'Contact': '/contact',
    'PrivacyPolicy': '/privacy-policy',
    'Terms': '/terms',
    'ServiceDetail': '/service-detail',
    'BlogPost': '/blog-post',
    'CaseStudyDetail': '/case-study-detail'
  };
  
  return pageRoutes[pageName] || '/';
};

// Format date utility
export const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

// Generate slug from title
export const generateSlug = (title) => {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9 -]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim('-');
};

// Get featured items
export const getFeaturedItems = (items) => {
  return items.filter(item => item.featured);
};

// Get items by category
export const getItemsByCategory = (items, category) => {
  return items.filter(item => item.category === category);
};
