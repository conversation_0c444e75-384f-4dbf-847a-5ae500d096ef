import React, { useState } from 'react';
import { X, Send, Download, Loader2, CheckCircle } from 'lucide-react';

export default function EmailCaptureModal({ resource, isOpen, onClose }) {
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  if (!isOpen) return null;

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!email || !name) {
        alert("Please provide your name and email.");
        return;
    }
    setIsSubmitting(true);

    try {
      // Simulate email sending and download
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setIsSubmitted(true);

      // Simulate download trigger
      console.log(`Downloading: ${resource.title} for ${name} (${email})`);

    } catch (error) {
      console.error('Error submitting email or downloading resource:', error);
      alert('There was an error. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const handleClose = () => {
      setEmail('');
      setName('');
      setIsSubmitted(false);
      onClose();
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4">
      <div className="bg-white brutal-border brutal-shadow p-8 max-w-lg w-full relative asymmetric-grid">
        <button 
          onClick={handleClose} 
          className="absolute -top-4 -right-4 bg-pink-500 text-white p-2 brutal-border brutal-shadow hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
        >
          <X className="w-6 h-6" />
        </button>

        {!isSubmitted ? (
          <>
            <h2 className="brutal-text text-2xl mb-2">GET YOUR FREE RESOURCE</h2>
            <p className="font-bold mb-6">
              Enter your email below to get instant access to <span className="text-pink-500">"{resource.title}"</span>.
            </p>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="brutal-text text-sm mb-2 block">YOUR NAME</label>
                <input
                  type="text"
                  placeholder="Enter your name..."
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="w-full px-4 py-3 brutal-border brutal-shadow-small font-bold focus:outline-none focus:translate-x-1 focus:translate-y-1 focus:shadow-none transition-all duration-150"
                  required
                />
              </div>
              <div>
                <label className="brutal-text text-sm mb-2 block">YOUR EMAIL</label>
                <input
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-4 py-3 brutal-border brutal-shadow-small font-bold focus:outline-none focus:translate-x-1 focus:translate-y-1 focus:shadow-none transition-all duration-150"
                  required
                />
              </div>
              <button 
                type="submit" 
                disabled={isSubmitting} 
                className="w-full bg-blue-500 text-white px-6 py-3 brutal-border brutal-shadow brutal-text hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150 disabled:opacity-50 inline-flex items-center justify-center"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" /> 
                    SENDING...
                  </>
                ) : (
                  <>
                    <Download className="mr-2 h-4 w-4" /> 
                    DOWNLOAD NOW
                  </>
                )}
              </button>
            </form>
          </>
        ) : (
          <div className="text-center">
            <div className="bg-green-400 w-20 h-20 brutal-border brutal-shadow mx-auto mb-6 flex items-center justify-center">
                <CheckCircle className="w-10 h-10 text-black" />
            </div>
            <h2 className="brutal-text text-2xl mb-4">SUCCESS!</h2>
            <p className="font-bold mb-4">
              Your download has started. We've also sent a copy to <span className="text-blue-500">{email}</span>.
            </p>
            <button 
              onClick={handleClose} 
              className="bg-black text-white px-6 py-3 brutal-border brutal-shadow brutal-text hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
            >
                CLOSE
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
