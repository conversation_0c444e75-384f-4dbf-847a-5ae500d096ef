import React, { useState, useEffect } from "react";
import { BlogPost } from "@/entities/BlogPost";
import { format } from "date-fns";
import { Clock, ArrowUpRight, Search, Filter } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

export default function Blog() {
  const [posts, setPosts] = useState([]);
  const [filteredPosts, setFilteredPosts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");

  const categories = [
    { value: "all", label: "ALL POSTS" },
    { value: "strategy", label: "STRATEGY" },
    { value: "tips", label: "TIPS" },
    { value: "case-studies", label: "CASE STUDIES" },
    { value: "industry-news", label: "INDUSTRY NEWS" },
    { value: "tutorials", label: "TUTORIALS" }
  ];

  useEffect(() => {
    loadPosts();
  }, []);

  useEffect(() => {
    filterPosts();
  }, [posts, searchTerm, selectedCategory]);

  const loadPosts = async () => {
    try {
      const blogPosts = await BlogPost.filter({ published: true }, '-created_date');
      setPosts(blogPosts);
    } catch (error) {
      console.error("Error loading blog posts:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const filterPosts = () => {
    let filtered = posts;

    if (selectedCategory !== "all") {
      filtered = filtered.filter(post => post.category === selectedCategory);
    }

    if (searchTerm) {
      filtered = filtered.filter(post => 
        post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.excerpt?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.tags?.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    setFilteredPosts(filtered);
  };

  const getCategoryColor = (category) => {
    const colors = {
      strategy: "bg-blue-500",
      tips: "bg-green-400", 
      "case-studies": "bg-pink-500",
      "industry-news": "bg-yellow-400",
      tutorials: "bg-purple-500"
    };
    return colors[category] || "bg-gray-500";
  };

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-green-400">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-black text-white p-8 brutal-border brutal-shadow inline-block transform -rotate-1 mb-8">
            <h1 className="brutal-text text-4xl md:text-6xl">THE BLOG</h1>
          </div>
          <p className="text-xl md:text-2xl font-bold text-black max-w-3xl mx-auto">
            Social media insights, growth strategies, and industry secrets. 
            No fluff, just actionable advice that actually works.
          </p>
        </div>
      </section>

      {/* Search and Filter */}
      <section className="py-8 bg-white brutal-border border-b-4">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                type="text"
                placeholder="Search articles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 brutal-border brutal-shadow-small"
              />
            </div>

            {/* Category Filter */}
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <Button
                  key={category.value}
                  variant={selectedCategory === category.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category.value)}
                  className={`brutal-border brutal-shadow-small brutal-text text-xs hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150 ${
                    selectedCategory === category.value 
                      ? 'bg-pink-500 text-white' 
                      : 'bg-white text-black hover:bg-yellow-400'
                  }`}
                >
                  {category.label}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Blog Posts */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div key={i} className="bg-gray-200 brutal-border brutal-shadow p-6 animate-pulse h-96"></div>
              ))}
            </div>
          ) : filteredPosts.length > 0 ? (
            <>
              {/* Featured Post */}
              {filteredPosts.length > 0 && (
                <div className="mb-16">
                  <div className="text-center mb-8">
                    <div className="bg-pink-500 text-white px-6 py-3 brutal-border brutal-shadow inline-block transform rotate-1">
                      <h2 className="brutal-text text-xl">FEATURED ARTICLE</h2>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <div className="asymmetric-grid">
                      <div className="bg-blue-500 text-white p-8 md:p-12 brutal-border brutal-shadow">
                        <div className="flex items-center gap-4 mb-6">
                          <div className={`${getCategoryColor(filteredPosts[0].category)} text-black px-3 py-1 brutal-text text-sm`}>
                            {filteredPosts[0].category?.replace('-', ' ').toUpperCase()}
                          </div>
                          {filteredPosts[0].read_time && (
                            <div className="flex items-center gap-1 text-sm opacity-90">
                              <Clock className="w-4 h-4" />
                              {filteredPosts[0].read_time} min read
                            </div>
                          )}
                        </div>
                        <h2 className="brutal-text text-3xl md:text-4xl mb-4">
                          {filteredPosts[0].title}
                        </h2>
                        {filteredPosts[0].excerpt && (
                          <p className="font-bold text-lg leading-relaxed mb-6">
                            {filteredPosts[0].excerpt}
                          </p>
                        )}
                        <div className="text-sm opacity-90">
                          {format(new Date(filteredPosts[0].created_date), "MMMM d, yyyy")}
                        </div>
                      </div>
                    </div>

                    <div className="anti-asymmetric">
                      {filteredPosts[0].featured_image_url && (
                        <div className="bg-gray-200 brutal-border brutal-shadow mb-6">
                          <img 
                            src={filteredPosts[0].featured_image_url} 
                            alt={filteredPosts[0].title}
                            className="w-full h-64 object-cover"
                          />
                        </div>
                      )}
                      <Button className="w-full bg-pink-500 text-white brutal-border brutal-shadow brutal-text hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150">
                        READ FULL ARTICLE
                        <ArrowUpRight className="w-5 h-5 ml-2" />
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {/* All Posts Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredPosts.slice(1).map((post, index) => (
                  <div key={post.id} className={index % 2 === 0 ? "asymmetric-grid" : "anti-asymmetric"}>
                    <div className="bg-white brutal-border brutal-shadow p-6 hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300 h-full cursor-pointer">
                      {post.featured_image_url && (
                        <div className="bg-gray-200 brutal-border brutal-shadow-small mb-4 overflow-hidden">
                          <img 
                            src={post.featured_image_url} 
                            alt={post.title}
                            className="w-full h-32 object-cover"
                          />
                        </div>
                      )}
                      
                      <div className="flex items-center gap-2 mb-4">
                        <div className={`${getCategoryColor(post.category)} text-black px-2 py-1 brutal-text text-xs`}>
                          {post.category?.replace('-', ' ').toUpperCase()}
                        </div>
                        {post.read_time && (
                          <div className="flex items-center gap-1 text-xs">
                            <Clock className="w-3 h-3" />
                            {post.read_time}m
                          </div>
                        )}
                      </div>
                      
                      <h3 className="brutal-text text-lg mb-3 line-clamp-2">
                        {post.title}
                      </h3>
                      
                      {post.excerpt && (
                        <p className="font-bold text-sm text-gray-800 mb-4 line-clamp-3">
                          {post.excerpt}
                        </p>
                      )}

                      <div className="flex items-center justify-between">
                        <div className="text-xs text-gray-500">
                          {format(new Date(post.created_date), "MMM d, yyyy")}
                        </div>
                        <ArrowUpRight className="w-4 h-4 text-pink-500" />
                      </div>
                      
                      {post.tags && post.tags.length > 0 && (
                        <div className="mt-4 pt-4 border-t-2 border-black">
                          <div className="flex flex-wrap gap-1">
                            {post.tags.slice(0, 3).map((tag, i) => (
                              <span key={i} className="bg-gray-100 text-black px-2 py-1 text-xs font-bold">
                                #{tag}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </>
          ) : (
            <div className="text-center">
              <div className="bg-gray-100 brutal-border brutal-shadow p-12 max-w-2xl mx-auto">
                <h2 className="brutal-text text-2xl mb-4">NO ARTICLES FOUND</h2>
                <p className="font-bold text-gray-600 mb-6">
                  {searchTerm || selectedCategory !== "all" 
                    ? "No articles match your search criteria. Try adjusting your filters." 
                    : "We're working on creating valuable content for you. Check back soon!"
                  }
                </p>
                {(searchTerm || selectedCategory !== "all") && (
                  <Button
                    onClick={() => {
                      setSearchTerm("");
                      setSelectedCategory("all");
                    }}
                    className="bg-pink-500 text-white brutal-border brutal-shadow brutal-text hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150"
                  >
                    CLEAR FILTERS
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>
      </section>
    </div>
  );
}