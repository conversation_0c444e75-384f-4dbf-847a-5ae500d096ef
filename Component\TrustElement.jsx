import React from "react";
import { Shield, Award, Users, CheckCircle, Star, Lock, Globe, TrendingUp } from "lucide-react";

export default function TrustElements({ showAll = true }) {
  const clientLogos = [
    "TECHFLOW SOLUTIONS", "FITLIFE GYM CHAIN", "LUXE BEAUTY CO", 
    "DIGITAL DYNAMICS", "GROWTHTECH", "BRANDIFY", 
    "SCALECORP", "NEXUS MEDIA", "PROPEL BRANDS"
  ];

  const certifications = [
    { 
      name: "Facebook Marketing Partner", 
      icon: CheckCircle, 
      color: "bg-blue-500",
      description: "Certified Facebook advertising partner"
    },
    { 
      name: "Google Partner", 
      icon: Award, 
      color: "bg-green-400",
      description: "Google Ads certified specialist"
    },
    { 
      name: "HubSpot Certified", 
      icon: Star, 
      color: "bg-pink-500",
      description: "Inbound marketing certification"
    },
    { 
      name: "Hootsuite Academy", 
      icon: Globe, 
      color: "bg-yellow-400",
      description: "Social media marketing expert"
    }
  ];

  const awards = [
    "Best Social Media Agency 2024",
    "Digital Marketing Excellence Award",
    "Top Rated SMMA - Clutch.co",
    "Rising Star Agency - Marketing Land"
  ];

  const securityBadges = [
    { name: "SSL Secured", icon: Lock, description: "Your data is encrypted and secure" },
    { name: "GDPR Compliant", icon: Shield, description: "Full privacy compliance" },
    { name: "SOC 2 Certified", icon: CheckCircle, description: "Enterprise security standards" }
  ];

  if (!showAll) {
    return (
      <div className="flex flex-wrap justify-center items-center gap-6 py-8">
        {certifications.slice(0, 2).map((cert, index) => (
          <div key={index} className={`${cert.color} text-white px-4 py-2 brutal-border brutal-shadow-small inline-flex items-center gap-2`}>
            <cert.icon className="w-5 h-5" />
            <span className="brutal-text text-sm">{cert.name}</span>
          </div>
        ))}
        {securityBadges.slice(0, 1).map((badge, index) => (
          <div key={index} className="bg-black text-white px-4 py-2 brutal-border brutal-shadow-small inline-flex items-center gap-2">
            <badge.icon className="w-5 h-5" />
            <span className="brutal-text text-sm">{badge.name}</span>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="py-20 bg-gray-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Client Logos Wall */}
        <section className="mb-16">
          <div className="text-center mb-8">
            <div className="bg-blue-500 text-white p-4 brutal-border brutal-shadow inline-block transform rotate-1">
              <h2 className="brutal-text text-2xl">TRUSTED BY GROWING BRANDS</h2>
            </div>
          </div>
          <div className="grid grid-cols-3 md:grid-cols-6 lg:grid-cols-9 gap-4">
            {clientLogos.map((logo, index) => (
              <div key={index} className={`bg-white brutal-border brutal-shadow-small p-3 text-center ${index % 2 === 0 ? 'asymmetric-grid' : 'anti-asymmetric'}`}>
                <div className="brutal-text text-xs text-gray-800">{logo}</div>
              </div>
            ))}
          </div>
        </section>

        {/* Certifications */}
        <section className="mb-16">
          <div className="text-center mb-8">
            <div className="bg-green-400 text-black p-4 brutal-border brutal-shadow inline-block transform -rotate-1">
              <h2 className="brutal-text text-2xl">INDUSTRY CERTIFICATIONS</h2>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {certifications.map((cert, index) => (
              <div key={index} className={`bg-white brutal-border brutal-shadow p-6 text-center ${index % 2 === 0 ? 'asymmetric-grid' : 'anti-asymmetric'}`}>
                <div className={`${cert.color} w-16 h-16 brutal-border brutal-shadow-small mx-auto mb-4 flex items-center justify-center`}>
                  <cert.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="brutal-text text-lg mb-2">{cert.name}</h3>
                <p className="font-bold text-sm text-gray-600">{cert.description}</p>
              </div>
            ))}
          </div>
        </section>

        {/* Awards & Recognition */}
        <section className="mb-16">
          <div className="text-center mb-8">
            <div className="bg-pink-500 text-white p-4 brutal-border brutal-shadow inline-block transform rotate-1">
              <h2 className="brutal-text text-2xl">AWARDS & RECOGNITION</h2>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {awards.map((award, index) => (
              <div key={index} className={`bg-white brutal-border brutal-shadow p-6 flex items-center gap-4 ${index % 2 === 0 ? 'asymmetric-grid' : 'anti-asymmetric'}`}>
                <div className="bg-yellow-400 w-12 h-12 brutal-border brutal-shadow-small flex items-center justify-center">
                  <Award className="w-6 h-6 text-black" />
                </div>
                <div>
                  <h3 className="brutal-text text-lg">{award}</h3>
                  <p className="font-bold text-sm text-gray-600">2024 Recognition</p>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Security & Trust Badges */}
        <section className="mb-16">
          <div className="text-center mb-8">
            <div className="bg-black text-white p-4 brutal-border brutal-shadow inline-block">
              <h2 className="brutal-text text-2xl">SECURITY & COMPLIANCE</h2>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {securityBadges.map((badge, index) => (
              <div key={index} className={`bg-white brutal-border brutal-shadow p-6 text-center ${index % 2 === 0 ? 'asymmetric-grid' : 'anti-asymmetric'}`}>
                <div className="bg-green-400 w-16 h-16 brutal-border brutal-shadow-small mx-auto mb-4 flex items-center justify-center">
                  <badge.icon className="w-8 h-8 text-black" />
                </div>
                <h3 className="brutal-text text-lg mb-2">{badge.name}</h3>
                <p className="font-bold text-sm text-gray-600">{badge.description}</p>
              </div>
            ))}
          </div>
        </section>

        {/* Money-Back Guarantee */}
        <section>
          <div className="bg-yellow-400 text-black p-8 brutal-border brutal-shadow text-center max-w-3xl mx-auto">
            <div className="bg-black text-white w-20 h-20 brutal-border brutal-shadow mx-auto mb-6 flex items-center justify-center">
              <Shield className="w-10 h-10" />
            </div>
            <h2 className="brutal-text text-3xl mb-4">30-DAY MONEY-BACK GUARANTEE</h2>
            <p className="text-xl font-bold mb-6">
              We're so confident in our ability to generate results that we offer a full refund if you're not satisfied within 30 days.
            </p>
            <p className="font-bold text-lg">
              No questions asked. No fine print. Just results or your money back.
            </p>
          </div>
        </section>

      </div>
    </div>
  );
}