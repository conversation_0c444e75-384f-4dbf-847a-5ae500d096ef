import React from "react";
import { Link } from "react-router-dom";
import { createPageUrl } from "@/utils";
import { But<PERSON> } from "@/components/ui/button";
import { 
  MessageCircle, 
  Target, 
  Users, 
  BarChart3, 
  Video, 
  Camera, 
  Megaphone, 
  TrendingUp,
  ArrowUpRight,
  CheckCircle
} from "lucide-react";

export default function Services() {
  const mainServices = [
    {
      icon: MessageCircle,
      title: "CONTENT CREATION",
      subtitle: "Stop the scroll, start the sales",
      description: "We create thumb-stopping content that doesn't just get likes – it gets customers. Every post is designed with conversion in mind.",
      features: [
        "Viral-worthy video content",
        "High-converting carousel posts",
        "Story content that sells",
        "Reels that actually reach people",
        "Brand-aligned visual identity"
      ],
      pricing: "Starting at $2,500/month",
      color: "bg-blue-500"
    },
    {
      icon: Target,
      title: "PAID ADVERTISING",
      subtitle: "Turn ad spend into revenue",
      description: "Facebook and Instagram ads that generate 3x+ ROAS consistently. We don't just run ads – we build profit machines.",
      features: [
        "Advanced audience targeting",
        "Creative testing & optimization", 
        "Landing page optimization",
        "Conversion tracking setup",
        "Weekly performance reports"
      ],
      pricing: "Starting at $3,000/month + ad spend",
      color: "bg-pink-500"
    },
    {
      icon: Users,
      title: "COMMUNITY MANAGEMENT",
      subtitle: "Build an army of brand advocates",
      description: "Turn your followers into a engaged community that sells for you. We handle everything from comments to DMs to crisis management.",
      features: [
        "Daily engagement management",
        "Community growth strategies",
        "Influencer outreach campaigns",
        "Crisis management & reputation",
        "Customer service via social"
      ],
      pricing: "Starting at $1,800/month",
      color: "bg-green-400"
    },
    {
      icon: BarChart3,
      title: "ANALYTICS & REPORTING", 
      subtitle: "Data that drives decisions",
      description: "Get crystal-clear insights into what's working, what's not, and exactly how to optimize every dollar you spend.",
      features: [
        "Custom dashboard setup",
        "Weekly performance reports",
        "Competitor analysis",
        "ROI tracking & attribution",
        "Monthly strategy sessions"
      ],
      pricing: "Included with all packages",
      color: "bg-yellow-400"
    }
  ];

  const addOnServices = [
    {
      icon: Video,
      title: "VIDEO PRODUCTION",
      description: "Professional video content that converts",
      price: "From $1,200/video"
    },
    {
      icon: Camera,
      title: "BRAND PHOTOGRAPHY",
      description: "Stunning visuals that tell your brand story",
      price: "From $800/shoot"
    },
    {
      icon: Megaphone,
      title: "INFLUENCER CAMPAIGNS",
      description: "Authentic partnerships that drive results",
      price: "From $2,000/campaign"
    },
    {
      icon: TrendingUp,
      title: "SOCIAL COMMERCE",
      description: "Turn your social platforms into sales channels",
      price: "Custom pricing"
    }
  ];

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-yellow-400">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-black text-white p-8 brutal-border brutal-shadow inline-block transform -rotate-1 mb-8">
            <h1 className="brutal-text text-4xl md:text-6xl">OUR SERVICES</h1>
          </div>
          <p className="text-xl md:text-2xl font-bold text-black max-w-3xl mx-auto">
            We don't do everything. We do these things better than anyone else. 
            Each service is designed to generate real revenue, not just engagement.
          </p>
        </div>
      </section>

      {/* Main Services */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-16">
            {mainServices.map((service, index) => (
              <div key={index} className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''}`}>
                <div className={index % 2 === 1 ? 'lg:col-start-2' : ''}>
                  <div className={`${service.color} text-white p-8 md:p-12 brutal-border brutal-shadow ${index % 2 === 0 ? 'asymmetric-grid' : 'anti-asymmetric'}`}>
                    <div className="bg-black text-white w-16 h-16 brutal-border brutal-shadow-small flex items-center justify-center mb-6">
                      <service.icon className="w-8 h-8" />
                    </div>
                    <h2 className="brutal-text text-3xl md:text-4xl mb-2">{service.title}</h2>
                    <p className="text-lg font-bold opacity-90 mb-6">{service.subtitle}</p>
                    <p className="font-bold text-lg leading-relaxed">
                      {service.description}
                    </p>
                  </div>
                </div>

                <div className={index % 2 === 1 ? 'lg:col-start-1 lg:row-start-1' : ''}>
                  <div className="bg-white brutal-border brutal-shadow p-8">
                    <div className="space-y-4 mb-8">
                      {service.features.map((feature, i) => (
                        <div key={i} className="flex items-center gap-3">
                          <div className="bg-green-400 brutal-border p-1">
                            <CheckCircle className="w-4 h-4 text-black" />
                          </div>
                          <span className="font-bold">{feature}</span>
                        </div>
                      ))}
                    </div>
                    
                    <div className="bg-gray-100 brutal-border p-4 mb-6">
                      <div className="brutal-text text-sm mb-2">INVESTMENT</div>
                      <div className="brutal-text text-xl">{service.pricing}</div>
                    </div>

                    <Button className="w-full bg-black text-white brutal-border brutal-shadow brutal-text hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150">
                      LEARN MORE
                      <ArrowUpRight className="w-5 h-5 ml-2" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Add-On Services */}
      <section className="py-20 bg-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="bg-pink-500 text-white p-6 brutal-border brutal-shadow inline-block transform rotate-1">
              <h2 className="brutal-text text-3xl md:text-4xl">ADD-ON SERVICES</h2>
            </div>
            <p className="text-xl font-bold text-white mt-8 max-w-2xl mx-auto">
              Level up your social media game with these specialized services.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {addOnServices.map((service, index) => (
              <div key={index} className={index % 2 === 0 ? "asymmetric-grid" : "anti-asymmetric"}>
                <div className="bg-white text-black p-6 brutal-border brutal-shadow hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300 h-full">
                  <div className="bg-yellow-400 w-12 h-12 brutal-border brutal-shadow-small flex items-center justify-center mb-4">
                    <service.icon className="w-6 h-6 text-black" />
                  </div>
                  <h3 className="brutal-text text-lg mb-2">{service.title}</h3>
                  <p className="font-bold text-gray-800 mb-4">{service.description}</p>
                  <div className="bg-blue-500 text-white px-3 py-1 brutal-text text-sm inline-block">
                    {service.price}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-green-400">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="bg-black text-white p-6 brutal-border brutal-shadow inline-block transform -rotate-1">
              <h2 className="brutal-text text-3xl md:text-4xl">OUR PROCESS</h2>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {[
              { step: "01", title: "AUDIT", description: "We analyze your current social presence and identify opportunities" },
              { step: "02", title: "STRATEGY", description: "Custom strategy built around your business goals and target audience" },
              { step: "03", title: "EXECUTE", description: "We implement the strategy while you focus on running your business" },
              { step: "04", title: "OPTIMIZE", description: "Continuous testing and optimization to maximize your ROI" }
            ].map((item, index) => (
              <div key={index} className={index % 2 === 0 ? "asymmetric-grid" : "anti-asymmetric"}>
                <div className="bg-white text-black p-6 brutal-border brutal-shadow text-center">
                  <div className="bg-pink-500 text-white w-12 h-12 brutal-border brutal-shadow-small flex items-center justify-center mx-auto mb-4">
                    <span className="brutal-text text-sm">{item.step}</span>
                  </div>
                  <h3 className="brutal-text text-lg mb-2">{item.title}</h3>
                  <p className="font-bold text-sm">{item.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-yellow-400 text-black p-8 md:p-12 brutal-border brutal-shadow">
            <h2 className="brutal-text text-3xl md:text-4xl mb-6">
              READY TO GET STARTED?
            </h2>
            <p className="text-xl font-bold mb-8">
              Book a free strategy call and we'll show you exactly how to turn your social media into a revenue-generating machine.
            </p>
            <Link to={createPageUrl("Contact")}>
              <Button className="bg-pink-500 text-white px-8 py-4 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150">
                BOOK FREE STRATEGY CALL
                <ArrowUpRight className="w-6 h-6 ml-2" />
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}