import React, { useState } from 'react';
import { InvokeLLM, SendEmail } from '@/integrations/Core';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Calculator, FileSearch, Send, Loader2 } from 'lucide-react';

export default function Tools() {
  // ROI Calculator State
  const [adSpend, setAdSpend] = useState(5000);
  const [cpc, setCpc] = useState(2.5);
  const [conversionRate, setConversionRate] = useState(3);
  const [saleValue, setSaleValue] = useState(150);
  const [roiResults, setRoiResults] = useState(null);

  // Social Audit State
  const [platform, setPlatform] = useState('');
  const [postFrequency, setPostFrequency] = useState(3);
  const [engagementRate, setEngagementRate] = useState('');
  const [hasBranding, setHasBranding] = useState('');
  const [paidAds, setPaidAds] = useState('');
  const [auditReport, setAuditReport] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [userName, setUserName] = useState('');
  const [userEmail, setUserEmail] = useState('');
  const [showEmailForm, setShowEmailForm] = useState(false);

  const handleCalculateROI = (e) => {
    e.preventDefault();
    const totalClicks = adSpend / cpc;
    const totalConversions = totalClicks * (conversionRate / 100);
    const totalRevenue = totalConversions * saleValue;
    const profit = totalRevenue - adSpend;
    const roi = (profit / adSpend) * 100;

    setRoiResults({
      totalRevenue: totalRevenue.toFixed(2),
      profit: profit.toFixed(2),
      roi: roi.toFixed(2),
      totalConversions: totalConversions.toFixed(2),
    });
  };

  const handleGenerateAudit = async (e) => {
    e.preventDefault();
    if (!platform || !postFrequency || !engagementRate || !hasBranding || !paidAds) {
      alert("Please fill out all audit fields.");
      return;
    }
    setShowEmailForm(true);
  };
  
  const handleSendAudit = async (e) => {
    e.preventDefault();
    if(!userName || !userEmail) {
        alert("Please enter your name and email.");
        return;
    }
    
    setIsGenerating(true);
    setAuditReport('');

    const prompt = `You are a social media marketing expert. A user has provided the following information for a quick audit:
- Primary Platform: ${platform}
- Posts per Week: ${postFrequency}
- Engagement Rate: ${engagementRate}
- Consistent Visual Branding: ${hasBranding}
- Running Paid Ads: ${paidAds}

Generate a short, actionable social media audit report based on this data. Structure the response with three sections: "STRENGTHS", "WEAKNESSES", and "TOP 3 RECOMMENDATIONS". The tone should be punchy, expert, and slightly informal. Do not use JSON, provide a plain text response. Start with "Here is your quick audit:".`;

    try {
      const report = await InvokeLLM({ prompt });
      setAuditReport(report);

      await SendEmail({
          to: userEmail,
          subject: "Your Free Social Media Audit from GrowthLab",
          body: `Hi ${userName},\n\nHere is your free social media audit report as requested:\n\n${report}\n\nReady to turn these insights into revenue? Book a free strategy call with us!\n\nBest,\nThe GrowthLab Team`,
          from_name: "GrowthLab SMMA"
      });

    } catch (error) {
      console.error("Error generating or sending audit:", error);
      setAuditReport("Sorry, there was an error generating your report. Please try again later.");
    } finally {
      setIsGenerating(false);
      setShowEmailForm(false);
    }
  };

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-yellow-400">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-black text-white p-8 brutal-border brutal-shadow inline-block transform -rotate-1 mb-8">
            <h1 className="brutal-text text-4xl md:text-6xl">FREE TOOLS</h1>
          </div>
          <p className="text-xl md:text-2xl font-bold text-black max-w-3xl mx-auto">
            Actionable tools to project your growth and audit your current strategy. No fluff, just data.
          </p>
        </div>
      </section>

      {/* Tools Section */}
      <div className="py-20 grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* ROI Calculator */}
        <div className="asymmetric-grid">
          <div className="bg-white brutal-border brutal-shadow p-8">
            <div className="flex items-center gap-4 mb-6">
              <div className="bg-blue-500 w-12 h-12 brutal-border brutal-shadow flex items-center justify-center">
                <Calculator className="w-6 h-6 text-white" />
              </div>
              <h2 className="brutal-text text-3xl">ROI CALCULATOR</h2>
            </div>
            <p className="font-bold mb-6">Estimate the potential return on your ad spend.</p>
            
            <form onSubmit={handleCalculateROI} className="space-y-6">
              <div>
                <label className="brutal-text text-sm mb-2 block">MONTHLY AD SPEND ($)</label>
                <Input type="number" value={adSpend} onChange={e => setAdSpend(Number(e.target.value))} className="brutal-border brutal-shadow-small" />
              </div>
              <div>
                <label className="brutal-text text-sm mb-2 block">AVG. COST PER CLICK (CPC) ($)</label>
                <Input type="number" step="0.01" value={cpc} onChange={e => setCpc(Number(e.target.value))} className="brutal-border brutal-shadow-small" />
              </div>
              <div>
                <label className="brutal-text text-sm mb-2 block">WEBSITE CONVERSION RATE (%)</label>
                <Input type="number" step="0.1" value={conversionRate} onChange={e => setConversionRate(Number(e.target.value))} className="brutal-border brutal-shadow-small" />
              </div>
              <div>
                <label className="brutal-text text-sm mb-2 block">AVG. SALE VALUE ($)</label>
                <Input type="number" value={saleValue} onChange={e => setSaleValue(Number(e.target.value))} className="brutal-border brutal-shadow-small" />
              </div>
              <Button type="submit" className="w-full bg-blue-500 text-white brutal-border brutal-shadow brutal-text hover:translate-x-1 hover:translate-y-1 hover:shadow-none">CALCULATE ROI</Button>
            </form>

            {roiResults && (
              <div className="mt-8 bg-blue-100 brutal-border brutal-shadow-small p-6">
                <h3 className="brutal-text text-xl mb-4 text-center">POTENTIAL RESULTS</h3>
                <div className="space-y-3">
                  <div className="flex justify-between font-bold"><span className="text-gray-700">Conversions:</span><span>{roiResults.totalConversions}</span></div>
                  <div className="flex justify-between font-bold"><span className="text-gray-700">Revenue:</span><span>${roiResults.totalRevenue}</span></div>
                  <div className="flex justify-between font-bold"><span className="text-gray-700">Profit:</span><span>${roiResults.profit}</span></div>
                  <div className="bg-black text-white p-3 mt-2 brutal-border">
                    <div className="flex justify-between brutal-text text-lg"><span className="text-yellow-400">ROI:</span><span className="text-green-400">{roiResults.roi}%</span></div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Social Media Audit */}
        <div className="anti-asymmetric">
          <div className="bg-white brutal-border brutal-shadow p-8">
            <div className="flex items-center gap-4 mb-6">
              <div className="bg-pink-500 w-12 h-12 brutal-border brutal-shadow flex items-center justify-center">
                <FileSearch className="w-6 h-6 text-white" />
              </div>
              <h2 className="brutal-text text-3xl">5-MINUTE SOCIAL AUDIT</h2>
            </div>
            <p className="font-bold mb-6">Get a free, AI-powered audit of your social media presence.</p>
            
            <form onSubmit={handleGenerateAudit} className="space-y-6">
               <div>
                  <label className="brutal-text text-sm mb-2 block">PRIMARY PLATFORM</label>
                  <Select onValueChange={setPlatform} value={platform}>
                    <SelectTrigger className="brutal-border brutal-shadow-small"><SelectValue placeholder="Select platform..." /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Instagram">Instagram</SelectItem>
                      <SelectItem value="Facebook">Facebook</SelectItem>
                      <SelectItem value="TikTok">TikTok</SelectItem>
                      <SelectItem value="LinkedIn">LinkedIn</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="brutal-text text-sm mb-2 block">POSTS PER WEEK: {postFrequency}</label>
                  <Slider value={[postFrequency]} onValueChange={([val]) => setPostFrequency(val)} max={21} step={1} />
                </div>
                 <div>
                  <label className="brutal-text text-sm mb-2 block">AVG. ENGAGEMENT RATE</label>
                  <Select onValueChange={setEngagementRate} value={engagementRate}>
                    <SelectTrigger className="brutal-border brutal-shadow-small"><SelectValue placeholder="Select rate..." /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Less than 1%">Less than 1% (Low)</SelectItem>
                      <SelectItem value="1-3%">1-3% (Average)</SelectItem>
                      <SelectItem value="3-5%">3-5% (Good)</SelectItem>
                      <SelectItem value="More than 5%">More than 5% (Excellent)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                 <div>
                  <label className="brutal-text text-sm mb-2 block">CONSISTENT BRANDING</label>
                  <Select onValueChange={setHasBranding} value={hasBranding}>
                    <SelectTrigger className="brutal-border brutal-shadow-small"><SelectValue placeholder="Is branding consistent?" /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Yes">Yes, very consistent</SelectItem>
                      <SelectItem value="Somewhat">Somewhat consistent</SelectItem>
                      <SelectItem value="No">No, not consistent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                 <div>
                  <label className="brutal-text text-sm mb-2 block">RUNNING PAID ADS</label>
                  <Select onValueChange={setPaidAds} value={paidAds}>
                    <SelectTrigger className="brutal-border brutal-shadow-small"><SelectValue placeholder="Are you using paid ads?" /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Yes, successfully">Yes, successfully</SelectItem>
                      <SelectItem value="Yes, but struggling">Yes, but struggling</SelectItem>
                      <SelectItem value="No">No, not yet</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {!showEmailForm && (
                    <Button type="submit" className="w-full bg-pink-500 text-white brutal-border brutal-shadow brutal-text hover:translate-x-1 hover:translate-y-1 hover:shadow-none">GET MY AUDIT</Button>
                )}
            </form>

            {showEmailForm && !auditReport && (
                 <form onSubmit={handleSendAudit} className="mt-8 bg-pink-100 p-6 brutal-border space-y-4">
                     <p className="font-bold text-center">Almost there! Where should we send your report?</p>
                     <div>
                        <label className="brutal-text text-sm mb-2 block">YOUR NAME</label>
                        <Input type="text" value={userName} onChange={e => setUserName(e.target.value)} className="brutal-border brutal-shadow-small bg-white" placeholder="Enter your name..."/>
                     </div>
                      <div>
                        <label className="brutal-text text-sm mb-2 block">YOUR EMAIL</label>
                        <Input type="email" value={userEmail} onChange={e => setUserEmail(e.target.value)} className="brutal-border brutal-shadow-small bg-white" placeholder="Enter your email..."/>
                     </div>
                     <Button type="submit" disabled={isGenerating} className="w-full bg-pink-500 text-white brutal-border brutal-shadow brutal-text hover:translate-x-1 hover:translate-y-1 hover:shadow-none disabled:opacity-50">
                         {isGenerating ? <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> GENERATING...</> : <>SEND MY FREE REPORT</>}
                     </Button>
                 </form>
            )}

            {auditReport && (
              <div className="mt-8 bg-pink-100 brutal-border brutal-shadow-small p-6 whitespace-pre-wrap font-bold">
                 <h3 className="brutal-text text-xl mb-4 text-center">AUDIT COMPLETE!</h3>
                 <p className="text-center mb-4">Your report has been sent to {userEmail}.</p>
                 <div className="bg-white p-4 brutal-border brutal-shadow-small">
                    {auditReport}
                 </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}