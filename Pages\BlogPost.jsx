import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { BlogPost } from "@/entities/BlogPost";
import { format } from "date-fns";
import { ArrowLeft, Clock, Share2, Tag, Calendar } from "lucide-react";
import { Button } from "@/components/ui/button";
import { createPageUrl } from "@/utils";
import ReactMarkdown from "react-markdown";

export default function BlogPostPage() {
  const { slug } = useParams();
  const [post, setPost] = useState(null);
  const [relatedPosts, setRelatedPosts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [notFound, setNotFound] = useState(false);

  useEffect(() => {
    loadPost();
    loadRelatedPosts();
  }, [slug]);

  const loadPost = async () => {
    try {
      const posts = await BlogPost.filter({ slug: slug });
      if (posts.length > 0) {
        setPost(posts[0]);
      } else {
        setNotFound(true);
      }
    } catch (error) {
      console.error("Error loading blog post:", error);
      setNotFound(true);
    } finally {
      setIsLoading(false);
    }
  };

  const loadRelatedPosts = async () => {
    try {
      const posts = await BlogPost.list('-created_date', 3);
      setRelatedPosts(posts);
    } catch (error) {
      console.error("Error loading related posts:", error);
    }
  };

  const getCategoryColor = (category) => {
    const colors = {
      strategy: "bg-blue-500",
      tips: "bg-green-400", 
      "case-studies": "bg-pink-500",
      "industry-news": "bg-yellow-400",
      tutorials: "bg-purple-500"
    };
    return colors[category] || "bg-gray-500";
  };

  const sharePost = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: post.title,
          text: post.excerpt,
          url: window.location.href,
        });
      } catch (error) {
        console.log("Error sharing:", error);
      }
    } else {
      navigator.clipboard.writeText(window.location.href);
      alert("Link copied to clipboard!");
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-white py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-gray-200 brutal-border brutal-shadow p-8 animate-pulse">
            <div className="h-8 bg-gray-300 mb-4"></div>
            <div className="h-4 bg-gray-300 mb-2"></div>
            <div className="h-4 bg-gray-300 w-3/4"></div>
          </div>
        </div>
      </div>
    );
  }

  if (notFound) {
    return (
      <div className="min-h-screen bg-white py-20 flex items-center justify-center">
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-red-100 text-black p-8 brutal-border brutal-shadow">
            <h1 className="brutal-text text-4xl mb-6">POST NOT FOUND</h1>
            <p className="text-xl font-bold mb-8">
              The blog post you're looking for doesn't exist.
            </p>
            <Link to={createPageUrl("Blog")}>
              <Button className="bg-pink-500 text-white brutal-border brutal-shadow brutal-text hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150">
                BACK TO BLOG
                <ArrowLeft className="w-5 h-5 ml-2" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white min-h-screen">
      {/* Header */}
      <section className="py-12 bg-gray-100 brutal-border border-b-4">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <Link 
            to={createPageUrl("Blog")}
            className="inline-flex items-center gap-2 mb-8 bg-white brutal-border brutal-shadow-small px-4 py-2 hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
          >
            <ArrowLeft className="w-4 h-4" />
            <span className="font-bold">BACK TO BLOG</span>
          </Link>

          <div className="flex items-center gap-4 mb-6">
            <div className={`${getCategoryColor(post.category)} text-white px-3 py-1 brutal-text text-sm`}>
              {post.category?.replace('-', ' ').toUpperCase()}
            </div>
            {post.read_time && (
              <div className="flex items-center gap-1 text-sm font-bold">
                <Clock className="w-4 h-4" />
                {post.read_time} min read
              </div>
            )}
            <div className="flex items-center gap-1 text-sm font-bold">
              <Calendar className="w-4 h-4" />
              {format(new Date(post.created_date), "MMM d, yyyy")}
            </div>
          </div>

          <h1 className="brutal-text text-3xl md:text-5xl text-black mb-6 leading-tight">
            {post.title}
          </h1>

          {post.excerpt && (
            <p className="text-xl font-bold text-gray-700 mb-6">
              {post.excerpt}
            </p>
          )}

          <div className="flex items-center justify-between">
            {post.tags && post.tags.length > 0 && (
              <div className="flex items-center gap-2">
                <Tag className="w-4 h-4" />
                <div className="flex flex-wrap gap-1">
                  {post.tags.map((tag, i) => (
                    <span key={i} className="bg-gray-200 text-black px-2 py-1 text-xs font-bold">
                      #{tag}
                    </span>
                  ))}
                </div>
              </div>
            )}

            <Button
              onClick={sharePost}
              variant="outline"
              size="sm"
              className="brutal-border brutal-shadow-small hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
            >
              <Share2 className="w-4 h-4 mr-2" />
              SHARE
            </Button>
          </div>
        </div>
      </section>

      {/* Featured Image */}
      {post.featured_image_url && (
        <section className="py-8">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-gray-200 brutal-border brutal-shadow overflow-hidden">
              <img 
                src={post.featured_image_url} 
                alt={post.title}
                className="w-full h-64 md:h-96 object-cover"
              />
            </div>
          </div>
        </section>
      )}

      {/* Content */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white brutal-border brutal-shadow p-8 md:p-12">
            <div className="prose prose-lg max-w-none">
              <ReactMarkdown
                components={{
                  h1: ({children}) => <h1 className="brutal-text text-3xl mb-6 mt-8">{children}</h1>,
                  h2: ({children}) => <h2 className="brutal-text text-2xl mb-4 mt-6">{children}</h2>,
                  h3: ({children}) => <h3 className="brutal-text text-xl mb-3 mt-5">{children}</h3>,
                  p: ({children}) => <p className="font-bold text-gray-800 mb-4 leading-relaxed">{children}</p>,
                  ul: ({children}) => <ul className="list-disc pl-6 mb-4 space-y-2">{children}</ul>,
                  li: ({children}) => <li className="font-bold text-gray-800">{children}</li>,
                  strong: ({children}) => <strong className="brutal-text">{children}</strong>,
                  blockquote: ({children}) => (
                    <div className="bg-yellow-400 text-black p-6 brutal-border brutal-shadow-small my-6">
                      {children}
                    </div>
                  )
                }}
              >
                {post.content}
              </ReactMarkdown>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-black">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-pink-500 text-white p-8 brutal-border brutal-shadow asymmetric-grid">
            <h2 className="brutal-text text-3xl mb-6">
              READY TO IMPLEMENT THESE STRATEGIES?
            </h2>
            <p className="text-xl font-bold mb-8">
              Stop reading about success and start creating it. 
              Let's build a social media strategy that actually generates revenue.
            </p>
            <Link to={createPageUrl("Contact")}>
              <Button className="bg-white text-black px-8 py-4 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150">
                GET YOUR FREE STRATEGY CALL
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Related Posts */}
      {relatedPosts.length > 0 && (
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <div className="bg-green-400 text-black p-6 brutal-border brutal-shadow inline-block transform -rotate-1">
                <h2 className="brutal-text text-2xl">RELATED ARTICLES</h2>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {relatedPosts.filter(p => p.id !== post.id).slice(0, 3).map((relatedPost, index) => (
                <div key={relatedPost.id} className={index % 2 === 0 ? "asymmetric-grid" : "anti-asymmetric"}>
                  <div className="bg-white brutal-border brutal-shadow p-6 hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300 h-full cursor-pointer">
                    {relatedPost.featured_image_url && (
                      <div className="bg-gray-200 brutal-border brutal-shadow-small mb-4 overflow-hidden">
                        <img 
                          src={relatedPost.featured_image_url} 
                          alt={relatedPost.title}
                          className="w-full h-32 object-cover"
                        />
                      </div>
                    )}
                    
                    <div className="flex items-center gap-2 mb-4">
                      <div className={`${getCategoryColor(relatedPost.category)} text-white px-2 py-1 brutal-text text-xs`}>
                        {relatedPost.category?.replace('-', ' ').toUpperCase()}
                      </div>
                      {relatedPost.read_time && (
                        <div className="flex items-center gap-1 text-xs">
                          <Clock className="w-3 h-3" />
                          {relatedPost.read_time}m
                        </div>
                      )}
                    </div>
                    
                    <h3 className="brutal-text text-lg mb-3 line-clamp-2">
                      {relatedPost.title}
                    </h3>
                    
                    {relatedPost.excerpt && (
                      <p className="font-bold text-sm text-gray-800 mb-4 line-clamp-3">
                        {relatedPost.excerpt}
                      </p>
                    )}

                    <div className="text-xs text-gray-500">
                      {format(new Date(relatedPost.created_date), "MMM d, yyyy")}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}
    </div>
  );
}