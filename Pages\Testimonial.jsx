import React, { useState, useEffect } from "react";
import { Testimonial } from "@/entities/Testimonial";
import { Star, Play, Quote, Filter } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export default function Testimonials() {
  const [testimonials, setTestimonials] = useState([]);
  const [filteredTestimonials, setFilteredTestimonials] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedService, setSelectedService] = useState("all");

  const services = [
    { value: "all", label: "ALL SERVICES" },
    { value: "Content Creation", label: "CONTENT CREATION" },
    { value: "Paid Advertising", label: "PAID ADVERTISING" },
    { value: "Community Management", label: "COMMUNITY MANAGEMENT" },
    { value: "Analytics & Reporting", label: "ANALYTICS & REPORTING" }
  ];

  useEffect(() => {
    loadTestimonials();
  }, []);

  useEffect(() => {
    filterTestimonials();
  }, [testimonials, selectedService]);

  const loadTestimonials = async () => {
    try {
      const testimonialData = await Testimonial.list('-created_date');
      setTestimonials(testimonialData);
    } catch (error) {
      console.error("Error loading testimonials:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const filterTestimonials = () => {
    let filtered = testimonials;
    
    if (selectedService !== "all") {
      filtered = filtered.filter(testimonial => 
        testimonial.service_used === selectedService
      );
    }

    setFilteredTestimonials(filtered);
  };

  const renderStars = (rating) => {
    return (
      <div className="flex gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`w-5 h-5 ${
              star <= rating 
                ? 'text-yellow-400 fill-current' 
                : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  return (
    <div className="bg-white min-h-screen">
      {/* Header */}
      <section className="py-20 bg-green-400">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-black text-white p-8 brutal-border brutal-shadow inline-block transform rotate-1 mb-8">
            <h1 className="brutal-text text-4xl md:text-6xl">TESTIMONIALS</h1>
          </div>
          <p className="text-xl md:text-2xl font-bold text-black max-w-3xl mx-auto">
            Don't just take our word for it. Here's what our clients say about working with GrowthLab SMMA.
          </p>
        </div>
      </section>

      {/* Filter */}
      <section className="py-8 bg-white brutal-border border-b-4">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center gap-4">
            <Filter className="w-5 h-5 text-gray-500" />
            <Select value={selectedService} onValueChange={setSelectedService}>
              <SelectTrigger className="w-64 brutal-border brutal-shadow-small">
                <SelectValue placeholder="Filter by service" />
              </SelectTrigger>
              <SelectContent>
                {services.map((service) => (
                  <SelectItem key={service.value} value={service.value}>
                    {service.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div key={i} className="bg-gray-200 brutal-border brutal-shadow p-6 animate-pulse h-80"></div>
              ))}
            </div>
          ) : filteredTestimonials.length > 0 ? (
            <>
              {/* Featured Testimonials */}
              {filteredTestimonials.filter(t => t.featured).length > 0 && (
                <div className="mb-16">
                  <div className="text-center mb-12">
                    <div className="bg-pink-500 text-white px-6 py-3 brutal-border brutal-shadow inline-block transform -rotate-1">
                      <h2 className="brutal-text text-2xl">FEATURED SUCCESS STORIES</h2>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                    {filteredTestimonials.filter(t => t.featured).slice(0, 2).map((testimonial, index) => (
                      <div key={testimonial.id} className={index % 2 === 0 ? "asymmetric-grid" : "anti-asymmetric"}>
                        <div className="bg-white brutal-border brutal-shadow p-8 hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300 h-full">
                          {/* Video Testimonial */}
                          {testimonial.video_url && (
                            <div className="bg-black brutal-border brutal-shadow-small mb-6 relative overflow-hidden">
                              <div className="aspect-video bg-gray-900 flex items-center justify-center">
                                <Button 
                                  onClick={() => window.open(testimonial.video_url, '_blank')}
                                  className="bg-pink-500 text-white w-16 h-16 rounded-full brutal-border brutal-shadow-small hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
                                >
                                  <Play className="w-8 h-8 ml-1" fill="currentColor" />
                                </Button>
                              </div>
                            </div>
                          )}

                          {/* Client Photo */}
                          <div className="flex items-center gap-4 mb-6">
                            {testimonial.client_photo_url && (
                              <div className="bg-gray-200 brutal-border brutal-shadow-small overflow-hidden">
                                <img 
                                  src={testimonial.client_photo_url} 
                                  alt={testimonial.client_name}
                                  className="w-16 h-16 object-cover"
                                />
                              </div>
                            )}
                            <div>
                              <h3 className="brutal-text text-lg">{testimonial.client_name}</h3>
                              <p className="font-bold text-gray-600 text-sm">
                                {testimonial.client_role} at {testimonial.client_company}
                              </p>
                              <div className="mt-2">{renderStars(testimonial.rating)}</div>
                            </div>
                          </div>

                          {/* Quote */}
                          <div className="bg-yellow-400 text-black p-6 brutal-border brutal-shadow-small mb-6 relative">
                            <Quote className="absolute top-2 left-2 w-6 h-6 opacity-50" />
                            <p className="font-bold text-lg leading-relaxed pl-8">
                              "{testimonial.testimonial_text}"
                            </p>
                          </div>

                          {/* Results & Service */}
                          <div className="space-y-3">
                            {testimonial.results_achieved && (
                              <div>
                                <span className="brutal-text text-sm text-gray-500">RESULTS ACHIEVED:</span>
                                <p className="font-bold text-green-600">{testimonial.results_achieved}</p>
                              </div>
                            )}
                            
                            {testimonial.service_used && (
                              <div className="bg-blue-500 text-white px-3 py-1 brutal-text text-sm inline-block">
                                {testimonial.service_used}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* All Testimonials Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredTestimonials.filter(t => !t.featured).map((testimonial, index) => (
                  <div key={testimonial.id} className={index % 2 === 0 ? "asymmetric-grid" : "anti-asymmetric"}>
                    <div className="bg-white brutal-border brutal-shadow p-6 hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300 h-full flex flex-col">
                      {/* Client Info */}
                      <div className="flex items-center gap-3 mb-4">
                        {testimonial.client_photo_url && (
                          <div className="bg-gray-200 brutal-border brutal-shadow-small overflow-hidden">
                            <img 
                              src={testimonial.client_photo_url} 
                              alt={testimonial.client_name}
                              className="w-12 h-12 object-cover"
                            />
                          </div>
                        )}
                        <div className="flex-1 min-w-0">
                          <h3 className="brutal-text text-md truncate">{testimonial.client_name}</h3>
                          <p className="font-bold text-gray-600 text-xs truncate">
                            {testimonial.client_role}{testimonial.client_company && ` at ${testimonial.client_company}`}
                          </p>
                        </div>
                      </div>

                      {/* Rating */}
                      <div className="mb-4">{renderStars(testimonial.rating)}</div>

                      {/* Quote */}
                      <div className="bg-gray-50 brutal-border brutal-shadow-small p-4 mb-4 flex-grow">
                        <p className="font-bold text-gray-800 text-sm leading-relaxed line-clamp-4">
                          "{testimonial.testimonial_text}"
                        </p>
                      </div>

                      {/* Video Button */}
                      {testimonial.video_url && (
                        <Button
                          onClick={() => window.open(testimonial.video_url, '_blank')}
                          variant="outline"
                          size="sm"
                          className="mb-3 brutal-border brutal-shadow-small hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
                        >
                          <Play className="w-4 h-4 mr-2" />
                          WATCH VIDEO
                        </Button>
                      )}

                      {/* Results & Service */}
                      <div className="space-y-2 mt-auto">
                        {testimonial.results_achieved && (
                          <div>
                            <p className="font-bold text-green-600 text-xs">
                              Results: {testimonial.results_achieved}
                            </p>
                          </div>
                        )}
                        
                        {testimonial.service_used && (
                          <div className="bg-pink-500 text-white px-2 py-1 brutal-text text-xs inline-block">
                            {testimonial.service_used}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          ) : (
            <div className="text-center">
              <div className="bg-gray-100 brutal-border brutal-shadow p-12 max-w-2xl mx-auto">
                <h2 className="brutal-text text-2xl mb-4">NO TESTIMONIALS FOUND</h2>
                <p className="font-bold text-gray-600 mb-6">
                  {selectedService !== "all"
                    ? "No testimonials found for the selected service. Try selecting 'All Services'." 
                    : "We're collecting amazing testimonials from our happy clients. Check back soon!"
                  }
                </p>
                {selectedService !== "all" && (
                  <Button
                    onClick={() => setSelectedService("all")}
                    className="bg-pink-500 text-white brutal-border brutal-shadow brutal-text hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150"
                  >
                    SHOW ALL TESTIMONIALS
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Trust Stats */}
      <section className="py-16 bg-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {[
              { stat: "4.9/5", label: "AVERAGE RATING" },
              { stat: "50+", label: "HAPPY CLIENTS" },
              { stat: "150%", label: "AVG GROWTH" },
              { stat: "98%", label: "RETENTION RATE" }
            ].map((item, index) => (
              <div key={index} className={`text-center ${index % 2 === 0 ? 'asymmetric-grid' : 'anti-asymmetric'}`}>
                <div className="bg-yellow-400 text-black p-6 brutal-border brutal-shadow">
                  <div className="brutal-text text-2xl md:text-3xl mb-2">{item.stat}</div>
                  <div className="font-bold text-sm">{item.label}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-blue-500 text-white p-8 md:p-12 brutal-border brutal-shadow">
            <h2 className="brutal-text text-3xl md:text-4xl mb-6">
              READY TO JOIN OUR SUCCESS STORIES?
            </h2>
            <p className="text-xl font-bold mb-8">
              Stop reading testimonials and start creating your own success story. 
              Book a free strategy call and see how we can transform your social media.
            </p>
            <Button className="bg-yellow-400 text-black px-8 py-4 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150">
              BECOME OUR NEXT SUCCESS STORY
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}