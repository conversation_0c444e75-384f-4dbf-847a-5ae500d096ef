import React from "react";
import { Link, useLocation } from "react-router-dom";
import { createPageUrl } from "@/utils";
import { Menu, X, ArrowUpRight, Zap } from "lucide-react";
import TrustElements from "./components/TrustElements";

export default function Layout({ children, currentPageName }) {
  const location = useLocation();
  const [mobileMenuOpen, setMobileMenuOpen] = React.useState(false);

  const navigation = [
    { name: "HOME", url: createPageUrl("Home") },
    { name: "ABOUT", url: createPageUrl("About") },
    { name: "SERVICES", url: createPageUrl("Services") },
    { name: "PROCESS", url: createPageUrl("Process") },
    { name: "CASE STUDIES", url: createPageUrl("CaseStudies") },
    { name: "TESTIMONIALS", url: createPageUrl("Testimonials") },
    { name: "RESOURCES", url: createPageUrl("Resources") },
    { name: "TOOLS", url: createPageUrl("Tools") },
    { name: "BLOG", url: createPageUrl("Blog") },
    { name: "FAQ", url: createPageUrl("FAQ") },
    { name: "CONTACT", url: createPageUrl("Contact") }
  ];

  const footerLinks = [
    { name: "Privacy Policy", url: createPageUrl("PrivacyPolicy") },
    { name: "Terms of Service", url: createPageUrl("Terms") }
  ];

  const isCurrentPage = (url) => {
    if (url === createPageUrl("Home")) {
      return location.pathname === "/" || location.pathname === createPageUrl("Home");
    }
    return location.pathname === url;
  };

  const showTrustElements = currentPageName === "Home" || currentPageName === "About";

  return (
    <div className="min-h-screen bg-white">
      <style>{`
        :root {
          --primary-blue: #0066FF;
          --primary-pink: #FF0066;
          --accent-yellow: #FFFF00;
          --accent-green: #00FF66;
          --brutal-black: #000000;
          --brutal-white: #FFFFFF;
        }
        
        .brutal-border {
          border: 4px solid var(--brutal-black);
        }
        
        .brutal-shadow {
          box-shadow: 8px 8px 0px var(--brutal-black);
        }
        
        .brutal-shadow-small {
          box-shadow: 4px 4px 0px var(--brutal-black);
        }
        
        .brutal-text {
          font-weight: 900;
          text-transform: uppercase;
          letter-spacing: -0.02em;
        }
        
        .asymmetric-grid {
          transform: rotate(-0.5deg);
        }
        
        .anti-asymmetric {
          transform: rotate(0.3deg);
        }
      `}</style>

      {/* Header */}
      <header className="brutal-border border-b-4 bg-white relative z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            {/* Logo */}
            <Link to={createPageUrl("Home")} className="flex items-center group">
              <div className="bg-black text-white px-4 py-2 brutal-border brutal-shadow-small group-hover:translate-x-1 group-hover:translate-y-1 group-hover:shadow-none transition-all duration-150">
                <div className="flex items-center gap-2">
                  <Zap className="w-6 h-6 text-yellow-400" fill="currentColor" />
                  <span className="brutal-text text-lg">GROWTHLAB</span>
                </div>
              </div>
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-1">
              {navigation.slice(0, 7).map((item) => (
                <Link
                  key={item.name}
                  to={item.url}
                  className={`px-4 py-3 brutal-text text-xs transition-all duration-150 hover:translate-x-1 hover:translate-y-1 ${
                    isCurrentPage(item.url)
                      ? 'bg-blue-500 text-white brutal-border brutal-shadow-small'
                      : 'text-black hover:bg-yellow-400 hover:brutal-border hover:brutal-shadow-small'
                  }`}
                >
                  {item.name}
                </Link>
              ))}
              
              {/* More Dropdown */}
              <div className="relative group">
                <button className="px-4 py-3 brutal-text text-xs text-black hover:bg-yellow-400 hover:brutal-border hover:brutal-shadow-small transition-all duration-150 hover:translate-x-1 hover:translate-y-1">
                  MORE
                </button>
                <div className="absolute top-full right-0 bg-white brutal-border brutal-shadow mt-2 min-w-48 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
                  {navigation.slice(7).map((item) => (
                    <Link
                      key={item.name}
                      to={item.url}
                      className={`block px-4 py-3 brutal-text text-xs transition-all duration-150 hover:bg-yellow-400 ${
                        isCurrentPage(item.url) ? 'bg-blue-500 text-white' : 'text-black'
                      }`}
                    >
                      {item.name}
                    </Link>
                  ))}
                </div>
              </div>
            </nav>

            {/* CTA Button */}
            <div className="hidden md:block">
              <Link
                to={createPageUrl("Contact")}
                className="bg-pink-500 text-white px-6 py-3 brutal-border brutal-shadow brutal-text text-sm hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 inline-flex items-center gap-2"
              >
                GET STARTED
                <ArrowUpRight className="w-4 h-4" />
              </Link>
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="lg:hidden brutal-border p-2 brutal-shadow-small hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
            >
              {mobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div className="lg:hidden brutal-border border-t-4 bg-white">
            <div className="px-4 pt-4 pb-6 space-y-2 max-h-96 overflow-y-auto">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.url}
                  onClick={() => setMobileMenuOpen(false)}
                  className={`block px-4 py-3 brutal-text text-sm transition-all duration-150 ${
                    isCurrentPage(item.url)
                      ? 'bg-blue-500 text-white brutal-border brutal-shadow-small'
                      : 'text-black hover:bg-yellow-400 hover:brutal-border hover:brutal-shadow-small'
                  }`}
                >
                  {item.name}
                </Link>
              ))}
              <Link
                to={createPageUrl("Contact")}
                onClick={() => setMobileMenuOpen(false)}
                className="block bg-pink-500 text-white px-4 py-3 brutal-border brutal-shadow brutal-text text-sm hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 text-center"
              >
                GET STARTED
              </Link>
            </div>
          </div>
        )}
      </header>

      {/* Main Content */}
      <main className="flex-1">
        {children}
        
        {/* Trust Elements on specific pages */}
        {showTrustElements && (
          <TrustElements showAll={currentPageName === "About"} />
        )}
      </main>

      {/* Footer */}
      <footer className="bg-black text-white brutal-border border-t-4 mt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            
            {/* Company Info */}
            <div className="asymmetric-grid">
              <div className="bg-yellow-400 text-black p-6 brutal-border brutal-shadow">
                <h3 className="brutal-text text-lg mb-4">GROWTHLAB SMMA</h3>
                <p className="text-sm font-bold">
                  We turn your social media into a customer-generating machine.
                </p>
              </div>
            </div>
            
            {/* Quick Links */}
            <div className="anti-asymmetric">
              <div className="bg-green-400 text-black p-6 brutal-border brutal-shadow">
                <h3 className="brutal-text text-lg mb-4">SERVICES</h3>
                <div className="space-y-2">
                  <Link to={createPageUrl("ServiceDetail?service=content-creation")} className="block font-bold hover:underline text-sm">
                    CONTENT CREATION
                  </Link>
                  <Link to={createPageUrl("ServiceDetail?service=paid-advertising")} className="block font-bold hover:underline text-sm">
                    PAID ADVERTISING  
                  </Link>
                  <Link to={createPageUrl("ServiceDetail?service=community-management")} className="block font-bold hover:underline text-sm">
                    COMMUNITY MANAGEMENT
                  </Link>
                  <Link to={createPageUrl("ServiceDetail?service=analytics-reporting")} className="block font-bold hover:underline text-sm">
                    ANALYTICS & REPORTING
                  </Link>
                </div>
              </div>
            </div>

            {/* Resources */}
            <div>
              <div className="bg-blue-500 text-white p-6 brutal-border brutal-shadow">
                <h3 className="brutal-text text-lg mb-4">RESOURCES</h3>
                <div className="space-y-2">
                  <Link to={createPageUrl("Blog")} className="block font-bold hover:underline text-sm">
                    BLOG
                  </Link>
                  <Link to={createPageUrl("CaseStudies")} className="block font-bold hover:underline text-sm">
                    CASE STUDIES
                  </Link>
                  <Link to={createPageUrl("Resources")} className="block font-bold hover:underline text-sm">
                    FREE RESOURCES
                  </Link>
                  <Link to={createPageUrl("FAQ")} className="block font-bold hover:underline text-sm">
                    FAQ
                  </Link>
                </div>
              </div>
            </div>

            {/* Contact Info */}
            <div className="asymmetric-grid">
              <div className="bg-pink-500 text-white p-6 brutal-border brutal-shadow">
                <h3 className="brutal-text text-lg mb-4">CONTACT INFO</h3>
                <div className="space-y-2 text-sm font-bold">
                  <p><EMAIL></p>
                  <p>+1 (555) 123-4567</p>
                  <p>123 Digital Street, Suite 100</p>
                  <p>Marketing City, MC 12345</p>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Footer */}
          <div className="border-t-4 border-white mt-8 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <p className="brutal-text text-sm">© 2024 GROWTHLAB SMMA. ALL RIGHTS RESERVED.</p>
              <div className="flex gap-4">
                {footerLinks.map((link) => (
                  <Link
                    key={link.name}
                    to={link.url}
                    className="text-sm font-bold hover:underline"
                  >
                    {link.name}
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}