import React, { useState } from "react";
import { ChevronDown, ChevronUp, HelpCircle, MessageCircle, Clock, DollarSign, Users, Target, ArrowUpRight } from "lucide-react";
import { Link } from "react-router-dom";
import { createPageUrl } from "@/utils";

export default function FAQ() {
  const [openFaq, setOpenFaq] = useState(null);

  const faqCategories = [
    {
      category: "GENERAL QUESTIONS",
      icon: HelpCircle,
      color: "bg-blue-500",
      faqs: [
        {
          question: "What makes GrowthLab different from other social media agencies?",
          answer: "We focus exclusively on revenue generation, not vanity metrics. While other agencies chase likes and followers, we build systems that convert social media traffic into actual customers and sales. Every strategy, post, and campaign is designed with your bottom line in mind."
        },
        {
          question: "How quickly can I expect to see results?",
          answer: "Most clients see improved engagement within 2-4 weeks, lead generation improvements within 6-8 weeks, and significant revenue impact within 90 days. However, results vary based on industry, competition, and current social media presence."
        },
        {
          question: "Do you work with businesses in all industries?",
          answer: "We work with most B2B and B2C businesses, but we specialize in industries where social media directly impacts purchasing decisions: SaaS, e-commerce, professional services, fitness, beauty, and local businesses. We avoid industries with heavy restrictions like gambling, adult content, or pharmaceuticals."
        },
        {
          question: "What platforms do you work with?",
          answer: "We primarily focus on Facebook, Instagram, LinkedIn, and TikTok as these platforms offer the best ROI for most businesses. We also work with Twitter, YouTube, and Pinterest based on your target audience and business goals."
        }
      ]
    },
    {
      category: "PRICING & PACKAGES",
      icon: DollarSign,
      color: "bg-green-400",
      faqs: [
        {
          question: "What are your pricing packages?",
          answer: "Our pricing depends on the services you need. Content creation starts at $2,500/month, paid advertising at $3,000/month plus ad spend, community management at $1,800/month, and full-service packages start at $6,000/month. All pricing includes strategy, execution, and reporting."
        },
        {
          question: "Are there any setup fees?",
          answer: "Yes, we charge a one-time setup fee of $1,500-$3,000 depending on the complexity of your account setup, tracking implementation, and initial strategy development. This ensures we start with a solid foundation."
        },
        {
          question: "What's included in the monthly fee?",
          answer: "Monthly fees include strategy development, content creation, posting, community management, campaign optimization, and detailed reporting. Ad spend is separate and goes directly to the platforms."
        },
        {
          question: "Do you offer any guarantees?",
          answer: "We offer a 30-day money-back guarantee if you're not satisfied with our work. While we can't guarantee specific results (no honest agency can), we guarantee professional service and strategic approach focused on your business goals."
        }
      ]
    },
    {
      category: "SERVICES & PROCESS",
      icon: Target,
      color: "bg-pink-500",
      faqs: [
        {
          question: "What does your onboarding process look like?",
          answer: "After signing, we conduct a comprehensive audit of your current social media presence, analyze your competitors, and develop a custom strategy. This includes setting up tracking, creating content calendars, and establishing clear KPIs and reporting schedules."
        },
        {
          question: "How do you measure success?",
          answer: "We track metrics that matter to your business: lead generation, cost per acquisition, conversion rates, and revenue attribution. We provide weekly reports showing exactly how social media impacts your bottom line, not just engagement statistics."
        },
        {
          question: "Can I approve content before it goes live?",
          answer: "Absolutely. We use an approval process where you review and approve content before publishing. For clients who prefer faster execution, we can work with brand guidelines and post-approval reviews."
        },
        {
          question: "What if I need to make changes to my package?",
          answer: "We're flexible. You can upgrade or downgrade services with 30 days notice. We understand business needs change, and we'll work with you to adjust our services accordingly."
        }
      ]
    },
    {
      category: "TECHNICAL & SUPPORT",
      icon: Users,
      color: "bg-yellow-400",
      faqs: [
        {
          question: "What access do you need to my accounts?",
          answer: "We need admin access to your social media business accounts and advertising accounts. We use secure access methods and never change passwords or critical account settings without permission. You maintain ultimate control of your accounts."
        },
        {
          question: "How often will we communicate?",
          answer: "We provide weekly email reports and are available for questions via email. Monthly strategy calls are included in most packages, and we're always available for urgent matters. Communication frequency can be adjusted based on your preferences."
        },
        {
          question: "What if I want to cancel?",
          answer: "We require 30 days written notice for cancellation. All paid fees for the current month are non-refundable, but we don't lock you into long-term contracts. We believe in earning your business every month through results."
        },
        {
          question: "Do you provide training for my team?",
          answer: "Yes, we offer training sessions for your internal team on social media best practices, platform updates, and how to maintain the systems we build. This is especially valuable for businesses who may eventually bring social media management in-house."
        }
      ]
    }
  ];

  const toggleFaq = (categoryIndex, faqIndex) => {
    const key = `${categoryIndex}-${faqIndex}`;
    setOpenFaq(openFaq === key ? null : key);
  };

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-purple-500">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white text-black p-8 brutal-border brutal-shadow inline-block transform -rotate-1 mb-8">
            <h1 className="brutal-text text-4xl md:text-6xl">FAQ</h1>
          </div>
          <p className="text-xl md:text-2xl font-bold text-white max-w-3xl mx-auto">
            Got questions? We've got answers. Everything you need to know about working with GrowthLab.
          </p>
        </div>
      </section>

      {/* FAQ Categories */}
      <section className="py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-12">
            {faqCategories.map((category, categoryIndex) => (
              <div key={categoryIndex} className={categoryIndex % 2 === 0 ? "asymmetric-grid" : "anti-asymmetric"}>
                <div className="bg-white brutal-border brutal-shadow p-8">
                  {/* Category Header */}
                  <div className="flex items-center gap-4 mb-8">
                    <div className={`${category.color} text-white w-12 h-12 brutal-border brutal-shadow-small flex items-center justify-center`}>
                      <category.icon className="w-6 h-6" />
                    </div>
                    <h2 className="brutal-text text-2xl">{category.category}</h2>
                  </div>

                  {/* FAQ Items */}
                  <div className="space-y-4">
                    {category.faqs.map((faq, faqIndex) => {
                      const isOpen = openFaq === `${categoryIndex}-${faqIndex}`;
                      return (
                        <div key={faqIndex} className="brutal-border">
                          <button
                            onClick={() => toggleFaq(categoryIndex, faqIndex)}
                            className="w-full p-4 text-left bg-gray-100 hover:bg-gray-200 transition-colors duration-200 flex items-center justify-between"
                          >
                            <span className="font-bold text-lg pr-4">{faq.question}</span>
                            {isOpen ? (
                              <ChevronUp className="w-5 h-5 flex-shrink-0" />
                            ) : (
                              <ChevronDown className="w-5 h-5 flex-shrink-0" />
                            )}
                          </button>
                          
                          {isOpen && (
                            <div className="p-4 bg-white border-t-2 border-black">
                              <p className="font-bold text-gray-700 leading-relaxed">
                                {faq.answer}
                              </p>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Still Have Questions Section */}
      <section className="py-20 bg-black">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-yellow-400 text-black p-8 md:p-12 brutal-border brutal-shadow">
            <div className="bg-black text-white w-20 h-20 brutal-border brutal-shadow mx-auto mb-6 flex items-center justify-center">
              <MessageCircle className="w-10 h-10" />
            </div>
            <h2 className="brutal-text text-3xl md:text-4xl mb-6">
              STILL HAVE QUESTIONS?
            </h2>
            <p className="text-xl font-bold mb-8">
              Can't find what you're looking for? Book a free strategy call and we'll answer all your questions personally.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                to={createPageUrl("Contact")}
                className="bg-pink-500 text-white px-8 py-4 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 inline-flex items-center justify-center"
              >
                BOOK FREE STRATEGY CALL
                <ArrowUpRight className="w-6 h-6 ml-2" />
              </Link>
              
              <a 
                href="mailto:<EMAIL>"
                className="bg-white text-black px-8 py-4 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 inline-flex items-center justify-center"
              >
                EMAIL US DIRECTLY
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
