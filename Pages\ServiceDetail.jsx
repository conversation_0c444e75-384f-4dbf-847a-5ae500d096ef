import React from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { ArrowLeft, CheckCircle, Star, Clock, Users, Target, MessageCircle, DollarSign } from "lucide-react";
import { Button } from "@/components/ui/button";
import { createPageUrl } from "@/utils";

export default function ServiceDetail() {
  const { service } = useParams();

  const serviceData = {
    "content-creation": {
      icon: MessageCircle,
      title: "CONTENT CREATION",
      subtitle: "Stop the scroll, start the sales",
      description: "We create thumb-stopping content that doesn't just get likes – it gets customers. Every post is designed with conversion in mind.",
      color: "bg-blue-500",
      features: [
        "Viral-worthy video content that drives engagement and conversions",
        "High-converting carousel posts that tell your brand story",
        "Story content that builds trust and drives immediate action",
        "Reels that actually reach people and generate leads", 
        "Brand-aligned visual identity across all platforms",
        "Content calendar planning and strategic posting schedules",
        "Trending hashtag research and optimization",
        "User-generated content campaigns that build community"
      ],
      process: [
        { step: "AUDIT", description: "We analyze your current content performance and identify opportunities" },
        { step: "STRATEGY", description: "Create a custom content strategy aligned with your business goals" },
        { step: "CREATION", description: "Our team produces high-quality, conversion-focused content" },
        { step: "OPTIMIZE", description: "Continuous testing and refinement to maximize engagement and ROI" }
      ],
      pricing: [
        { plan: "STARTER", price: "$2,500", features: ["20 posts/month", "2 video content pieces", "Basic graphics", "Hashtag research"] },
        { plan: "GROWTH", price: "$4,000", features: ["40 posts/month", "5 video content pieces", "Advanced graphics", "Story templates", "Content calendar"] },
        { plan: "PREMIUM", price: "$6,500", features: ["60 posts/month", "10 video content pieces", "Premium graphics", "UGC campaigns", "Dedicated manager"] }
      ],
      faqs: [
        { q: "How long does it take to see results?", a: "Most clients see improved engagement within 2-4 weeks, with lead generation typically improving within 6-8 weeks as we optimize content performance." },
        { q: "Do you create content for all social platforms?", a: "Yes, we create platform-specific content optimized for Facebook, Instagram, LinkedIn, TikTok, and Twitter based on your target audience." },
        { q: "Can I review content before it's published?", a: "Absolutely. All content goes through our approval process and you have final say before anything goes live." },
        { q: "What if I'm not happy with the content?", a: "We offer unlimited revisions within reason and a 30-day money-back guarantee if you're not satisfied." }
      ]
    },
    "paid-advertising": {
      icon: Target,
      title: "PAID ADVERTISING",
      subtitle: "Turn ad spend into revenue",
      description: "Facebook and Instagram ads that generate 3x+ ROAS consistently. We don't just run ads – we build profit machines.",
      color: "bg-pink-500",
      features: [
        "Advanced audience targeting using custom and lookalike audiences",
        "Creative testing and optimization for maximum performance",
        "Landing page optimization and conversion rate improvement", 
        "Conversion tracking setup and attribution modeling",
        "Weekly performance reports with actionable insights",
        "Budget optimization and bid strategy management",
        "Retargeting campaigns for maximum conversion",
        "A/B testing of ad creative, copy, and audiences"
      ],
      process: [
        { step: "SETUP", description: "Install tracking, create audiences, and set up conversion funnels" },
        { step: "LAUNCH", description: "Deploy targeted campaigns with proven ad formats and copy" },
        { step: "TEST", description: "Continuous testing of creative, audiences, and bidding strategies" },
        { step: "SCALE", description: "Scale winning campaigns while maintaining profitable ROAS" }
      ],
      pricing: [
        { plan: "STARTER", price: "$3,000", features: ["$5K+ ad spend", "2 platforms", "Basic reporting", "Monthly optimization"] },
        { plan: "GROWTH", price: "$5,000", features: ["$15K+ ad spend", "3 platforms", "Advanced reporting", "Bi-weekly optimization"] },
        { plan: "PREMIUM", price: "$8,000", features: ["$30K+ ad spend", "All platforms", "Real-time dashboard", "Weekly optimization"] }
      ],
      faqs: [
        { q: "What's your average ROAS?", a: "Our clients typically see 3-5x ROAS, with some achieving 8-10x once campaigns are fully optimized." },
        { q: "How much should I spend on ads?", a: "We recommend starting with at least $3,000/month in ad spend to gather sufficient data for optimization." },
        { q: "Do you work with small businesses?", a: "Yes, we work with businesses of all sizes, from startups to enterprise companies." },
        { q: "What platforms do you advertise on?", a: "We specialize in Facebook, Instagram, Google Ads, LinkedIn, and TikTok advertising." }
      ]
    },
    "community-management": {
      icon: Users,
      title: "COMMUNITY MANAGEMENT", 
      subtitle: "Build an army of brand advocates",
      description: "Turn your followers into an engaged community that sells for you. We handle everything from comments to DMs to crisis management.",
      color: "bg-green-400",
      features: [
        "Daily engagement management and response to all interactions",
        "Community growth strategies that attract your ideal customers",
        "Influencer outreach campaigns and partnership management",
        "Crisis management and reputation monitoring",
        "Customer service via social media channels",
        "Community events and engagement campaigns",
        "Brand advocacy programs and user-generated content",
        "Social listening and competitor monitoring"
      ],
      process: [
        { step: "LISTEN", description: "Monitor all mentions, comments, and messages across platforms" },
        { step: "ENGAGE", description: "Respond promptly and authentically to build relationships" },
        { step: "GROW", description: "Implement growth strategies to expand your community" },
        { step: "NURTURE", description: "Convert community members into customers through strategic engagement" }
      ],
      pricing: [
        { plan: "BASIC", price: "$1,800", features: ["Daily monitoring", "Response management", "Basic reporting", "Crisis alerts"] },
        { plan: "STANDARD", price: "$3,200", features: ["Advanced engagement", "Influencer outreach", "Community campaigns", "Detailed analytics"] },
        { plan: "PREMIUM", price: "$5,500", features: ["Full community management", "Brand advocacy programs", "24/7 monitoring", "Dedicated manager"] }
      ],
      faqs: [
        { q: "How quickly do you respond to comments?", a: "We aim to respond to all comments and messages within 2-4 hours during business hours, and within 24 hours on weekends." },
        { q: "Can you handle negative comments?", a: "Yes, we have extensive experience in crisis management and turning negative situations into positive outcomes." },
        { q: "Do you create engagement campaigns?", a: "Absolutely. We design and execute campaigns like contests, Q&As, and user-generated content drives." },
        { q: "What if there's a PR crisis?", a: "We have a crisis management protocol and will immediately alert you while taking appropriate action to protect your brand." }
      ]
    },
    "analytics-reporting": {
      icon: DollarSign,
      title: "ANALYTICS & REPORTING",
      subtitle: "Data that drives decisions", 
      description: "Get crystal-clear insights into what's working, what's not, and exactly how to optimize every dollar you spend.",
      color: "bg-yellow-400",
      features: [
        "Custom dashboard setup with real-time performance metrics",
        "Weekly performance reports with actionable recommendations",
        "Competitor analysis and benchmarking against industry standards",
        "ROI tracking and attribution modeling across all channels",
        "Monthly strategy sessions to review performance and plan ahead",
        "Advanced analytics setup and conversion tracking",
        "Performance forecasting and budget optimization",
        "Cross-platform attribution and customer journey mapping"
      ],
      process: [
        { step: "SETUP", description: "Install tracking codes and configure analytics platforms" },
        { step: "MEASURE", description: "Track all relevant metrics and KPIs for your business goals" },
        { step: "ANALYZE", description: "Identify trends, opportunities, and areas for improvement" },
        { step: "REPORT", description: "Deliver clear, actionable reports with strategic recommendations" }
      ],
      pricing: [
        { plan: "BASIC", price: "Included", features: ["Monthly reports", "Basic tracking", "Performance overview", "Email delivery"] },
        { plan: "ADVANCED", price: "$1,500", features: ["Weekly reports", "Custom dashboard", "Advanced analytics", "Strategy calls"] },
        { plan: "ENTERPRISE", price: "$3,000", features: ["Real-time dashboard", "Custom tracking", "Dedicated analyst", "Executive reporting"] }
      ],
      faqs: [
        { q: "What metrics do you track?", a: "We track everything from engagement and reach to leads, sales, and ROI. Our focus is always on metrics that impact your bottom line." },
        { q: "How often do I get reports?", a: "Standard reporting is monthly, but we offer weekly or even daily reporting for clients who need more frequent updates." },
        { q: "Can you track offline conversions?", a: "Yes, we can set up tracking for phone calls, in-store visits, and other offline conversions attributed to social media." },
        { q: "Do you provide competitor analysis?", a: "Yes, we monitor your competitors' social media performance and provide insights on opportunities and threats." }
      ]
    }
  };

  const currentService = serviceData[service];

  if (!currentService) {
    return (
      <div className="min-h-screen bg-white py-20 flex items-center justify-center">
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-red-100 text-black p-8 brutal-border brutal-shadow">
            <h1 className="brutal-text text-4xl mb-6">SERVICE NOT FOUND</h1>
            <p className="text-xl font-bold mb-8">
              The service you're looking for doesn't exist.
            </p>
            <Link to={createPageUrl("Services")}>
              <Button className="bg-pink-500 text-white brutal-border brutal-shadow brutal-text hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150">
                BACK TO SERVICES
                <ArrowLeft className="w-5 h-5 ml-2" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white min-h-screen">
      {/* Header */}
      <section className={`py-20 ${currentService.color}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Link 
            to={createPageUrl("Services")}
            className="inline-flex items-center gap-2 mb-8 bg-white text-black brutal-border brutal-shadow-small px-4 py-2 hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
          >
            <ArrowLeft className="w-4 h-4" />
            <span className="font-bold">BACK TO SERVICES</span>
          </Link>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="bg-white text-black p-8 brutal-border brutal-shadow">
                <div className="bg-black text-white w-16 h-16 brutal-border brutal-shadow-small flex items-center justify-center mb-6">
                  <currentService.icon className="w-8 h-8" />
                </div>
                <h1 className="brutal-text text-4xl md:text-5xl mb-4">
                  {currentService.title}
                </h1>
                <p className="text-xl font-bold text-pink-500 mb-6">
                  {currentService.subtitle}
                </p>
                <p className="font-bold text-lg text-gray-700">
                  {currentService.description}
                </p>
              </div>
            </div>

            <div>
              <div className="bg-black text-white p-8 brutal-border brutal-shadow">
                <h2 className="brutal-text text-2xl mb-6">WHY CHOOSE US?</h2>
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Star className="w-6 h-6 text-yellow-400 fill-current" />
                    <span className="font-bold">Proven Results</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Clock className="w-6 h-6 text-green-400" />
                    <span className="font-bold">Fast Implementation</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Users className="w-6 h-6 text-blue-400" />
                    <span className="font-bold">Dedicated Team</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Target className="w-6 h-6 text-pink-400" />
                    <span className="font-bold">Revenue-Focused</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="bg-pink-500 text-white p-6 brutal-border brutal-shadow inline-block transform rotate-1">
              <h2 className="brutal-text text-3xl">WHAT'S INCLUDED</h2>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {currentService.features.map((feature, index) => (
              <div key={index} className={index % 2 === 0 ? "asymmetric-grid" : "anti-asymmetric"}>
                <div className="bg-white brutal-border brutal-shadow p-6 hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-300">
                  <div className="flex items-start gap-4">
                    <div className="bg-green-400 brutal-border p-2 mt-1">
                      <CheckCircle className="w-5 h-5 text-black" />
                    </div>
                    <p className="font-bold text-gray-800">{feature}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Process */}
      <section className="py-20 bg-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="bg-blue-500 text-white p-6 brutal-border brutal-shadow inline-block transform -rotate-1">
              <h2 className="brutal-text text-3xl">OUR PROCESS</h2>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {currentService.process.map((step, index) => (
              <div key={index} className={index % 2 === 0 ? "asymmetric-grid" : "anti-asymmetric"}>
                <div className="bg-white text-black p-6 brutal-border brutal-shadow text-center h-full">
                  <div className="bg-yellow-400 text-black w-12 h-12 brutal-border brutal-shadow-small flex items-center justify-center mx-auto mb-4">
                    <span className="brutal-text text-sm">{(index + 1).toString().padStart(2, '0')}</span>
                  </div>
                  <h3 className="brutal-text text-lg mb-3">{step.step}</h3>
                  <p className="font-bold text-sm">{step.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section className="py-20 bg-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="bg-green-400 text-black p-6 brutal-border brutal-shadow inline-block transform rotate-1">
              <h2 className="brutal-text text-3xl">PRICING PACKAGES</h2>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {currentService.pricing.map((plan, index) => (
              <div key={index} className={index % 2 === 0 ? "asymmetric-grid" : "anti-asymmetric"}>
                <div className="bg-white text-black p-8 brutal-border brutal-shadow hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300 h-full">
                  <div className="text-center mb-6">
                    <h3 className="brutal-text text-xl mb-2">{plan.plan}</h3>
                    <div className="brutal-text text-3xl text-pink-500">{plan.price}</div>
                    <div className="text-sm font-bold text-gray-600">per month</div>
                  </div>

                  <div className="space-y-3 mb-8">
                    {plan.features.map((feature, i) => (
                      <div key={i} className="flex items-center gap-3">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        <span className="font-bold text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <Link to={createPageUrl("Contact")} className="w-full">
                    <Button className="w-full bg-pink-500 text-white brutal-border brutal-shadow brutal-text hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150">
                      GET STARTED
                    </Button>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="bg-yellow-400 text-black p-6 brutal-border brutal-shadow inline-block transform -rotate-1">
              <h2 className="brutal-text text-3xl">FREQUENTLY ASKED QUESTIONS</h2>
            </div>
          </div>

          <div className="space-y-6">
            {currentService.faqs.map((faq, index) => (
              <div key={index} className={index % 2 === 0 ? "asymmetric-grid" : "anti-asymmetric"}>
                <div className="bg-white brutal-border brutal-shadow p-6">
                  <h3 className="brutal-text text-lg mb-3">{faq.q}</h3>
                  <p className="font-bold text-gray-800">{faq.a}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="py-20 bg-pink-500">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white text-black p-8 md:p-12 brutal-border brutal-shadow">
            <h2 className="brutal-text text-3xl md:text-4xl mb-6">
              READY TO GET STARTED?
            </h2>
            <p className="text-xl font-bold mb-8">
              Book a free strategy call and we'll show you exactly how our {currentService.title.toLowerCase()} service can generate revenue for your business.
            </p>
            <Link to={createPageUrl("Contact")}>
              <Button className="bg-pink-500 text-white px-8 py-4 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150">
                BOOK FREE STRATEGY CALL
                <Target className="w-6 h-6 ml-2" />
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}