{"name": "Testimonial", "type": "object", "properties": {"client_name": {"type": "string", "description": "Name of the client"}, "client_company": {"type": "string", "description": "Client's company name"}, "client_role": {"type": "string", "description": "Client's job title"}, "client_photo_url": {"type": "string", "description": "<PERSON><PERSON>'s profile photo"}, "testimonial_text": {"type": "string", "description": "The testimonial quote"}, "rating": {"type": "number", "minimum": 1, "maximum": 5, "description": "Star rating out of 5"}, "service_used": {"type": "string", "description": "Which service they used"}, "video_url": {"type": "string", "description": "URL to video testimonial"}, "results_achieved": {"type": "string", "description": "Specific results they got"}, "featured": {"type": "boolean", "default": false, "description": "Show on homepage"}}, "required": ["client_name", "testimonial_text", "rating"]}