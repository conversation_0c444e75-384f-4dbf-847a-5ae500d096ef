import React, { useState, useEffect } from "react";
import { TeamMember } from "@/entities/TeamMember";
import { Zap, Target, Users, TrendingUp, Linkedin, Mail } from "lucide-react";

export default function About() {
  const [team, setTeam] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadTeam();
  }, []);

  const loadTeam = async () => {
    try {
      const teamData = await TeamMember.list('order_index');
      setTeam(teamData);
    } catch (error) {
      console.error("Error loading team:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const values = [
    {
      icon: Target,
      title: "RESULTS OBSESSED",
      description: "We don't care about vanity metrics. We care about revenue, leads, and real business growth."
    },
    {
      icon: Zap,
      title: "MOVE FAST",
      description: "While your competitors are still planning, we're already executing and optimizing."
    },
    {
      icon: Users,
      title: "PARTNERSHIP",
      description: "We're not just another vendor. We're your growth partners invested in your success."
    },
    {
      icon: TrendingUp,
      title: "DATA DRIVEN",
      description: "Every decision is backed by data. Every strategy is proven. Every result is measurable."
    }
  ];

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-pink-500">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="bg-white text-black p-8 brutal-border brutal-shadow inline-block transform rotate-1 mb-8">
              <h1 className="brutal-text text-4xl md:text-6xl">ABOUT GROWTHLAB</h1>
            </div>
            <div className="max-w-4xl mx-auto">
              <p className="text-xl md:text-2xl font-bold text-white mb-8">
                We're not your typical social media agency. We're growth hackers, data nerds, and revenue generators who happen to be really good at social media.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="asymmetric-grid">
              <div className="bg-yellow-400 text-black p-8 brutal-border brutal-shadow">
                <h2 className="brutal-text text-3xl md:text-4xl mb-6">OUR STORY</h2>
                <div className="space-y-4 font-bold">
                  <p>
                    Started in 2022 by a team of frustrated marketers who were tired of agencies that focused on "brand awareness" instead of bottom-line results.
                  </p>
                  <p>
                    We saw too many businesses burning money on social media without seeing real returns. So we built something different.
                  </p>
                  <p>
                    Today, we've generated over $10M in revenue for our clients and built a reputation as the agency that actually moves the needle.
                  </p>
                </div>
              </div>
            </div>

            <div className="anti-asymmetric">
              <div className="bg-green-400 text-black p-8 brutal-border brutal-shadow">
                <h2 className="brutal-text text-3xl md:text-4xl mb-6">OUR MISSION</h2>
                <div className="space-y-4 font-bold">
                  <p>
                    To eliminate the BS from social media marketing and build systems that actually generate revenue.
                  </p>
                  <p>
                    No vanity metrics. No "brand awareness" fluff. Just cold, hard results that show up on your bottom line.
                  </p>
                  <p>
                    We're here to prove that social media isn't just for memes – it's a legitimate revenue channel when done right.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="bg-blue-500 text-white p-6 brutal-border brutal-shadow inline-block transform -rotate-1">
              <h2 className="brutal-text text-3xl md:text-4xl">OUR VALUES</h2>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {values.map((value, index) => (
              <div key={index} className={index % 2 === 0 ? "asymmetric-grid" : "anti-asymmetric"}>
                <div className="bg-white text-black p-8 brutal-border brutal-shadow hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300 h-full">
                  <div className="bg-yellow-400 w-16 h-16 brutal-border brutal-shadow-small flex items-center justify-center mb-6">
                    <value.icon className="w-8 h-8 text-black" />
                  </div>
                  <h3 className="brutal-text text-xl mb-4">{value.title}</h3>
                  <p className="font-bold text-gray-800">{value.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="bg-pink-500 text-white p-6 brutal-border brutal-shadow inline-block transform rotate-1">
              <h2 className="brutal-text text-3xl md:text-4xl">MEET THE TEAM</h2>
            </div>
            <p className="text-xl font-bold text-black mt-8 max-w-2xl mx-auto">
              The growth hackers, strategists, and creative minds behind your success.
            </p>
          </div>

          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[1, 2, 3].map((i) => (
                <div key={i} className="bg-gray-200 brutal-border brutal-shadow p-8 animate-pulse h-96"></div>
              ))}
            </div>
          ) : team.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {team.map((member, index) => (
                <div key={member.id} className={index % 2 === 0 ? "asymmetric-grid" : "anti-asymmetric"}>
                  <div className="bg-white brutal-border brutal-shadow p-6 hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300">
                    {member.photo_url && (
                      <div className="bg-yellow-400 brutal-border brutal-shadow-small mb-6 overflow-hidden">
                        <img 
                          src={member.photo_url} 
                          alt={member.name}
                          className="w-full h-48 object-cover"
                        />
                      </div>
                    )}
                    
                    <h3 className="brutal-text text-xl mb-2">{member.name}</h3>
                    <div className="bg-blue-500 text-white px-3 py-1 brutal-text text-xs inline-block mb-4">
                      {member.role}
                    </div>
                    
                    <p className="font-bold text-gray-800 mb-4 text-sm">
                      {member.bio}
                    </p>

                    {member.specialties && member.specialties.length > 0 && (
                      <div className="mb-4">
                        <div className="flex flex-wrap gap-1">
                          {member.specialties.map((specialty, i) => (
                            <span key={i} className="bg-green-400 text-black px-2 py-1 text-xs font-bold">
                              {specialty}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                    <div className="flex gap-2">
                      {member.email && (
                        <a 
                          href={`mailto:${member.email}`}
                          className="bg-black text-white p-2 brutal-border brutal-shadow-small hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
                        >
                          <Mail className="w-4 h-4" />
                        </a>
                      )}
                      {member.linkedin_url && (
                        <a 
                          href={member.linkedin_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="bg-black text-white p-2 brutal-border brutal-shadow-small hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
                        >
                          <Linkedin className="w-4 h-4" />
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center">
              <div className="bg-gray-100 brutal-border brutal-shadow p-12">
                <p className="brutal-text text-xl">TEAM PROFILES COMING SOON</p>
                <p className="font-bold text-gray-600 mt-4">
                  We're busy generating results for our clients. Team bios are being updated.
                </p>
              </div>
            </div>
          )}
        </div>
      </section>
    </div>
  );
}